import cv2
import numpy as np
import mss
import time
import os
from typing import List, Tuple, Optional, Union, Dict, Any
import pyautogui
import math
import json
import base64
from datetime import datetime
import pickle

class ImageColorScript:
    """
    完整的图色脚本功能类
    包含区域找图、屏幕找图、区域找色、多点找色、截图、区域截图等功能
    """
    
    def __init__(self):
        """初始化图色脚本"""
        self.screen_width, self.screen_height = pyautogui.size()
        # 禁用 pyautogui 的安全检查
        pyautogui.FAILSAFE = False
        # 字库管理器
        self.font_manager = FontLibraryManager()
        
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        截取屏幕图像
        
        Args:
            region: 截图区域 (x, y, width, height)，None 表示全屏
            
        Returns:
            截取的图像数组 (BGR 格式)
        """
        with mss.mss() as sct:
            if region is None:
                # 全屏截图
                monitor = sct.monitors[1]
                img = sct.grab(monitor)
            else:
                # 区域截图
                x, y, width, height = region
                monitor = {"top": y, "left": x, "width": width, "height": height}
                img = sct.grab(monitor)
            
            img_np = np.array(img)
            img_bgr = cv2.cvtColor(img_np, cv2.COLOR_BGRA2BGR)
            return img_bgr
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None) -> bool:
        """
        保存截图到文件
        
        Args:
            filename: 保存的文件名
            region: 截图区域 (x, y, width, height)，None 表示全屏
            
        Returns:
            是否保存成功
        """
        try:
            img = self.capture_screen(region)
            cv2.imwrite(filename, img)
            return True
        except Exception as e:
            print(f"保存截图失败：{e}")
            return False
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        获取指定坐标的颜色值
        
        Args:
            x: X 坐标
            y: Y 坐标
            
        Returns:
            颜色值 (B, G, R)
        """
        img = self.capture_screen(region=(x, y, 1, 1))
        return tuple(img[0, 0])
    
    def color_distance(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """
        计算两个颜色之间的距离
        
        Args:
            color1: 颜色 1 (B, G, R)
            color2: 颜色 2 (B, G, R)
            
        Returns:
            颜色距离
        """
        return math.sqrt(sum((a - b) ** 2 for a, b in zip(color1, color2)))
    
    def find_image(self, template_path: str, region: Optional[Tuple[int, int, int, int]] = None, 
                   threshold: float = 0.8, max_results: int = 1) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找图像
        
        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量
            
        Returns:
            找到的位置列表 [(x, y, width, height), ...]
        """
        # 读取模板图像
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            raise FileNotFoundError(f"模板文件 {template_path} 不存在")
        
        # 截取搜索区域
        screen_img = self.capture_screen(region)
        
        # 执行模板匹配
        result = cv2.matchTemplate(screen_img, template, cv2.TM_CCOEFF_NORMED)
        
        # 查找匹配位置
        locations = np.where(result >= threshold)
        h, w = template.shape[:2]
        
        # 获取所有匹配位置
        matches = []
        for pt in zip(*locations[::-1]):
            x, y = pt
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((x, y, w, h))
        
        # 非极大值抑制去重
        if len(matches) > 0:
            matches = self._non_max_suppression(matches)
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_image_center(self, template_path: str, region: Optional[Tuple[int, int, int, int]] = None, 
                         threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        查找图像并返回中心坐标
        
        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            
        Returns:
            图像中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_image(template_path, region, threshold, max_results=1)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None
    
    def _non_max_suppression(self, boxes: List[Tuple[int, int, int, int]], 
                           overlap_threshold: float = 0.3) -> List[Tuple[int, int, int, int]]:
        """
        非极大值抑制去重
        
        Args:
            boxes: 边界框列表 [(x, y, w, h), ...]
            overlap_threshold: 重叠阈值
            
        Returns:
            去重后的边界框列表
        """
        if len(boxes) == 0:
            return []
        
        boxes = np.array(boxes)
        pick = []
        
        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 0] + boxes[:, 2]
        y2 = boxes[:, 1] + boxes[:, 3]
        
        area = (x2 - x1 + 1) * (y2 - y1 + 1)
        idxs = np.argsort(y2)
        
        while len(idxs) > 0:
            last = len(idxs) - 1
            i = idxs[last]
            pick.append(i)
            
            xx1 = np.maximum(x1[i], x1[idxs[:last]])
            yy1 = np.maximum(y1[i], y1[idxs[:last]])
            xx2 = np.minimum(x2[i], x2[idxs[:last]])
            yy2 = np.minimum(y2[i], y2[idxs[:last]])
            
            w = np.maximum(0, xx2 - xx1 + 1)
            h = np.maximum(0, yy2 - yy1 + 1)
            
            overlap = (w * h) / area[idxs[:last]]
            
            idxs = np.delete(idxs, np.concatenate(([last], np.where(overlap > overlap_threshold)[0])))
        
        return [tuple(boxes[i]) for i in pick]
    
    def find_color(self, target_color: Tuple[int, int, int], region: Optional[Tuple[int, int, int, int]] = None,
                   tolerance: int = 10, max_results: int = 1) -> List[Tuple[int, int]]:
        """
        在屏幕或指定区域查找颜色
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差
            max_results: 最大返回结果数量
            
        Returns:
            找到的坐标列表 [(x, y), ...]
        """
        # 截取搜索区域
        img = self.capture_screen(region)
        
        # 创建颜色掩码
        lower_bound = np.array([max(0, c - tolerance) for c in target_color])
        upper_bound = np.array([min(255, c + tolerance) for c in target_color])
        
        mask = cv2.inRange(img, lower_bound, upper_bound)
        
        # 查找匹配的像素点
        y_coords, x_coords = np.where(mask == 255)
        
        # 转换为坐标列表
        matches = []
        for x, y in zip(x_coords, y_coords):
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((int(x), int(y)))
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_color_center(self, target_color: Tuple[int, int, int], 
                         region: Optional[Tuple[int, int, int, int]] = None,
                         tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        查找颜色并返回第一个匹配点的坐标
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差
            
        Returns:
            第一个匹配点的坐标 (x, y)，未找到返回 None
        """
        results = self.find_color(target_color, region, tolerance, max_results=1)
        return results[0] if results else None

    def find_multi_color(self, color_points: List[Tuple[int, int, Tuple[int, int, int]]],
                        region: Optional[Tuple[int, int, int, int]] = None,
                        tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        多点找色

        Args:
            color_points: 颜色点列表 [(相对 x, 相对 y, (B, G, R)), ...]
                         第一个点为基准点，其他点为相对于基准点的偏移
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差

        Returns:
            基准点的坐标 (x, y)，未找到返回 None
        """
        if not color_points:
            return None

        # 第一个点作为基准点
        base_offset_x, base_offset_y, base_color = color_points[0]

        # 查找基准点的所有可能位置
        base_matches = self.find_color(base_color, region, tolerance, max_results=1000)

        # 检查每个基准点位置
        for base_x, base_y in base_matches:
            all_match = True

            # 检查其他所有点
            for offset_x, offset_y, target_color in color_points[1:]:
                check_x = base_x + offset_x - base_offset_x
                check_y = base_y + offset_y - base_offset_y

                # 检查坐标是否在有效范围内
                if (check_x < 0 or check_y < 0 or
                    check_x >= self.screen_width or check_y >= self.screen_height):
                    all_match = False
                    break

                # 获取该位置的颜色
                actual_color = self.get_pixel_color(check_x, check_y)

                # 检查颜色是否匹配
                if self.color_distance(actual_color, target_color) > tolerance * math.sqrt(3):
                    all_match = False
                    break

            if all_match:
                return (base_x, base_y)

        return None

    # ==================== 鼠标键盘操作 ====================

    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, interval: float = 0.1):
        """
        鼠标点击

        Args:
            x: X 坐标
            y: Y 坐标
            button: 鼠标按键 ('left', 'right', 'middle')
            clicks: 点击次数
            interval: 点击间隔
        """
        pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)

    def double_click(self, x: int, y: int):
        """双击"""
        pyautogui.doubleClick(x, y)

    def right_click(self, x: int, y: int):
        """右键点击"""
        pyautogui.rightClick(x, y)

    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 1.0):
        """
        拖拽操作

        Args:
            start_x: 起始 X 坐标
            start_y: 起始 Y 坐标
            end_x: 结束 X 坐标
            end_y: 结束 Y 坐标
            duration: 拖拽持续时间
        """
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)

    def scroll(self, x: int, y: int, clicks: int):
        """
        滚轮操作

        Args:
            x: X 坐标
            y: Y 坐标
            clicks: 滚动次数，正数向上，负数向下
        """
        pyautogui.scroll(clicks, x=x, y=y)

    def type_text(self, text: str, interval: float = 0.1):
        """
        输入文本

        Args:
            text: 要输入的文本
            interval: 字符间隔
        """
        pyautogui.typewrite(text, interval=interval)

    def press_key(self, key: str):
        """
        按键

        Args:
            key: 按键名称 (如 'enter', 'space', 'ctrl', 'alt' 等)
        """
        pyautogui.press(key)

    def key_combination(self, *keys):
        """
        组合键

        Args:
            keys: 按键组合 (如 'ctrl', 'c')
        """
        pyautogui.hotkey(*keys)

    # ==================== 图像处理和比较 ====================

    def compare_images(self, img1_path: str, img2_path: str, method: str = 'histogram') -> float:
        """
        比较两张图片的相似度

        Args:
            img1_path: 图片 1 路径
            img2_path: 图片 2 路径
            method: 比较方法 ('histogram', 'mse', 'ssim', 'template')

        Returns:
            相似度 (0-1)，1 表示完全相同
        """
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)

        if img1 is None or img2 is None:
            return 0.0

        if method == 'histogram':
            return self._compare_histogram(img1, img2)
        elif method == 'mse':
            return self._compare_mse(img1, img2)
        elif method == 'ssim':
            return self._compare_ssim(img1, img2)
        elif method == 'template':
            return self._compare_template(img1, img2)
        else:
            return self._compare_histogram(img1, img2)

    def _compare_histogram(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用直方图比较图像相似度"""
        # 转换为 HSV 颜色空间
        hsv1 = cv2.cvtColor(img1, cv2.COLOR_BGR2HSV)
        hsv2 = cv2.cvtColor(img2, cv2.COLOR_BGR2HSV)

        # 计算直方图
        hist1 = cv2.calcHist([hsv1], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])
        hist2 = cv2.calcHist([hsv2], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])

        # 使用相关性比较
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        return max(0.0, correlation)  # 确保返回值在 0-1 之间

    def _compare_mse(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用均方误差比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 计算均方误差
        mse = np.mean((img1.astype(float) - img2.astype(float)) ** 2)

        # 转换为相似度 (MSE 越小，相似度越高)
        max_mse = 255.0 ** 2  # 最大可能的 MSE
        similarity = 1.0 - (mse / max_mse)
        return max(0.0, similarity)

    def _compare_ssim(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用结构相似性指数比较图像"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算 SSIM (简化版本)
        mu1 = cv2.GaussianBlur(gray1.astype(float), (11, 11), 1.5)
        mu2 = cv2.GaussianBlur(gray2.astype(float), (11, 11), 1.5)

        mu1_sq = mu1 * mu1
        mu2_sq = mu2 * mu2
        mu1_mu2 = mu1 * mu2

        sigma1_sq = cv2.GaussianBlur(gray1.astype(float) * gray1.astype(float), (11, 11), 1.5) - mu1_sq
        sigma2_sq = cv2.GaussianBlur(gray2.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu2_sq
        sigma12 = cv2.GaussianBlur(gray1.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu1_mu2

        c1 = (0.01 * 255) ** 2
        c2 = (0.03 * 255) ** 2

        ssim_map = ((2 * mu1_mu2 + c1) * (2 * sigma12 + c2)) / ((mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2))
        return float(np.mean(ssim_map))

    def _compare_template(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用模板匹配比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 使用模板匹配
        result = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)
        return max(0.0, float(np.max(result)))  # 确保返回值非负

    def find_image_in_image(self, large_img_path: str, small_img_path: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        在大图中查找小图的位置（专门用于区域图在全屏图中的定位）

        Args:
            large_img_path: 大图路径
            small_img_path: 小图路径
            threshold: 相似度阈值

        Returns:
            小图在大图中的位置 (x, y)，未找到返回 None
        """
        large_img = cv2.imread(large_img_path)
        small_img = cv2.imread(small_img_path)

        if large_img is None or small_img is None:
            return None

        # 转换为灰度图
        large_gray = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
        small_gray = cv2.cvtColor(small_img, cv2.COLOR_BGR2GRAY)

        # 模板匹配
        result = cv2.matchTemplate(large_gray, small_gray, cv2.TM_CCOEFF_NORMED)

        # 查找最佳匹配位置
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val >= threshold:
            return max_loc
        else:
            return None

    def wait_for_image(self, template_path: str, timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      threshold: float = 0.8, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待图像出现

        Args:
            template_path: 模板图像路径
            timeout: 超时时间（秒）
            region: 搜索区域
            threshold: 相似度阈值
            check_interval: 检查间隔

        Returns:
            图像中心坐标，超时返回 None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_image_center(template_path, region, threshold)
            if result:
                return result
            time.sleep(check_interval)

        return None

    def wait_for_color(self, target_color: Tuple[int, int, int], timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      tolerance: int = 10, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待颜色出现

        Args:
            target_color: 目标颜色
            timeout: 超时时间（秒）
            region: 搜索区域
            tolerance: 颜色容差
            check_interval: 检查间隔

        Returns:
            颜色坐标，超时返回 None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_color_center(target_color, region, tolerance)
            if result:
                return result
            time.sleep(check_interval)

        return None

    # ==================== 字库和文字识别功能 ====================

    def load_font_library(self, library_path: str, library_name: str = "default") -> bool:
        """
        加载字库文件

        Args:
            library_path: 字库文件路径
            library_name: 字库名称

        Returns:
            是否加载成功
        """
        return self.font_manager.load_library(library_path, library_name)

    def find_character(self, char: str, region: Optional[Tuple[int, int, int, int]] = None,
                      threshold: float = 0.8, max_results: int = 1,
                      library_name: str = None) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找字符

        Args:
            char: 要查找的字符
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            找到的位置列表 [(x, y, width, height), ...]
        """
        # 获取字符模板
        templates = self.font_manager.get_character_templates(char, library_name)
        if not templates:
            print(f"字符 '{char}' 在字库中不存在")
            return []

        # 截取搜索区域
        screen_img = self.capture_screen(region)

        all_matches = []

        # 对每个模板进行匹配
        for template_data in templates:
            try:
                # 解码模板图像
                img_data = base64.b64decode(template_data["image"])
                img_array = np.frombuffer(img_data, np.uint8)
                template = cv2.imdecode(img_array, cv2.IMREAD_GRAYSCALE)

                if template is None:
                    continue

                # 转换屏幕图像为灰度
                screen_gray = cv2.cvtColor(screen_img, cv2.COLOR_BGR2GRAY)

                # 执行模板匹配
                result = cv2.matchTemplate(screen_gray, template, cv2.TM_CCOEFF_NORMED)

                # 查找匹配位置
                locations = np.where(result >= threshold)
                h, w = template.shape[:2]

                # 添加匹配结果
                for pt in zip(*locations[::-1]):
                    x, y = pt
                    # 如果指定了搜索区域，需要转换为屏幕坐标
                    if region is not None:
                        x += region[0]
                        y += region[1]

                    # 添加相似度信息
                    similarity = result[pt[1], pt[0]]
                    all_matches.append((x, y, w, h, similarity))

            except Exception as e:
                print(f"处理模板时出错：{e}")
                continue

        # 按相似度排序并去重
        if all_matches:
            # 按相似度降序排序
            all_matches.sort(key=lambda x: x[4], reverse=True)

            # 非极大值抑制去重
            boxes = [(x, y, w, h) for x, y, w, h, _ in all_matches]
            unique_boxes = self._non_max_suppression(boxes)

            return unique_boxes[:max_results]

        return []

    def find_character_center(self, char: str, region: Optional[Tuple[int, int, int, int]] = None,
                             threshold: float = 0.8, library_name: str = None) -> Optional[Tuple[int, int]]:
        """
        查找字符并返回中心坐标

        Args:
            char: 要查找的字符
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            字符中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_character(char, region, threshold, max_results=1, library_name=library_name)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None

    def find_text(self, text: str, region: Optional[Tuple[int, int, int, int]] = None,
                  threshold: float = 0.8, char_spacing: int = 5,
                  library_name: str = None) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找文字串

        Args:
            text: 要查找的文字串
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            char_spacing: 字符间最大间距
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            找到的文字串位置列表 [(x, y, width, height), ...]
        """
        if not text:
            return []

        # 查找第一个字符的所有位置
        first_char = text[0]
        first_char_positions = self.find_character(first_char, region, threshold, max_results=100, library_name=library_name)

        if not first_char_positions:
            return []

        text_matches = []

        # 对每个第一个字符的位置，尝试匹配完整文字串
        for first_x, first_y, first_w, first_h in first_char_positions:
            match_success = True
            current_x = first_x + first_w
            text_width = first_w
            text_height = first_h

            # 逐个匹配后续字符
            for i, char in enumerate(text[1:], 1):
                # 在当前位置附近查找字符
                search_region = (current_x, first_y - char_spacing,
                               char_spacing * 3, first_h + char_spacing * 2)

                char_positions = self.find_character(char, search_region, threshold, max_results=1, library_name=library_name)

                if not char_positions:
                    match_success = False
                    break

                char_x, char_y, char_w, char_h = char_positions[0]

                # 检查字符位置是否合理（垂直对齐，水平间距合理）
                if (abs(char_y - first_y) > char_spacing or
                    char_x - current_x > char_spacing):
                    match_success = False
                    break

                # 更新文字串的边界
                text_width = char_x + char_w - first_x
                text_height = max(text_height, char_h)
                current_x = char_x + char_w

            if match_success:
                text_matches.append((first_x, first_y, text_width, text_height))

        return text_matches

    def find_text_center(self, text: str, region: Optional[Tuple[int, int, int, int]] = None,
                        threshold: float = 0.8, char_spacing: int = 5,
                        library_name: str = None) -> Optional[Tuple[int, int]]:
        """
        查找文字串并返回中心坐标

        Args:
            text: 要查找的文字串
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            char_spacing: 字符间最大间距
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            文字串中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_text(text, region, threshold, char_spacing, library_name)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None


# ==================== 字库制作功能 ====================

class FontLibraryCreator:
    """
    字库制作类
    用于从图像中提取字符，创建字库文件
    """

    def __init__(self):
        """初始化字库制作器"""
        self.library_data = {
            "version": "1.0",
            "characters": {},
            "metadata": {
                "created_time": datetime.now().isoformat(),
                "total_chars": 0,
                "description": ""
            }
        }

    def extract_characters_from_image(self, image_path: str, text_content: str,
                                    auto_segment: bool = True,
                                    char_spacing: int = 2) -> Dict[str, List[np.ndarray]]:
        """
        从图像中提取字符

        Args:
            image_path: 包含文字的图像路径
            text_content: 图像中的文字内容（用于标注）
            auto_segment: 是否自动分割字符
            char_spacing: 字符间距（用于分割）

        Returns:
            字符字典 {字符: [图像数组列表]}
        """
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            raise FileNotFoundError(f"无法读取图像文件：{image_path}")

        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        if auto_segment:
            # 自动分割字符
            char_images = self._auto_segment_characters(gray, char_spacing)
        else:
            # 手动分割（返回整个图像，需要用户手动处理）
            char_images = [gray]

        # 将分割的字符与文字内容对应
        char_dict = {}
        if len(char_images) == len(text_content):
            for i, char in enumerate(text_content):
                if char not in char_dict:
                    char_dict[char] = []
                char_dict[char].append(char_images[i])
        else:
            # 如果数量不匹配，返回原图像供手动处理
            print(f"警告：检测到 {len(char_images)} 个字符，但文本有 {len(text_content)} 个字符")
            char_dict["_unmatched_"] = char_images

        return char_dict

    def _auto_segment_characters(self, gray_img: np.ndarray, char_spacing: int = 2) -> List[np.ndarray]:
        """
        自动分割字符

        Args:
            gray_img: 灰度图像
            char_spacing: 字符间距

        Returns:
            分割后的字符图像列表
        """
        # 二值化
        _, binary = cv2.threshold(gray_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 获取边界框并排序
        bounding_boxes = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 过滤太小的区域
            if w > 5 and h > 5:
                bounding_boxes.append((x, y, w, h))

        # 按 x 坐标排序
        bounding_boxes.sort(key=lambda box: box[0])

        # 提取字符图像
        char_images = []
        for x, y, w, h in bounding_boxes:
            char_img = gray_img[y:y+h, x:x+w]
            char_images.append(char_img)

        return char_images

    def extract_features(self, char_img: np.ndarray) -> Dict[str, Any]:
        """
        提取字符特征

        Args:
            char_img: 字符图像

        Returns:
            特征字典
        """
        features = {}

        # 基本尺寸特征
        features['width'] = char_img.shape[1]
        features['height'] = char_img.shape[0]
        features['aspect_ratio'] = features['width'] / features['height']

        # 二值化
        _, binary = cv2.threshold(char_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 像素密度特征
        total_pixels = char_img.shape[0] * char_img.shape[1]
        white_pixels = np.sum(binary == 255)
        features['density'] = white_pixels / total_pixels

        # 轮廓特征
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            features['contour_area'] = cv2.contourArea(largest_contour)
            features['contour_perimeter'] = cv2.arcLength(largest_contour, True)
        else:
            features['contour_area'] = 0
            features['contour_perimeter'] = 0

        # 投影特征（水平和垂直投影）
        h_projection = np.sum(binary, axis=0)
        v_projection = np.sum(binary, axis=1)
        features['h_projection'] = h_projection.tolist()
        features['v_projection'] = v_projection.tolist()

        return features

    def add_character_to_library(self, char: str, char_img: np.ndarray,
                                font_info: Optional[Dict[str, Any]] = None):
        """
        添加字符到字库

        Args:
            char: 字符
            char_img: 字符图像
            font_info: 字体信息
        """
        if char not in self.library_data["characters"]:
            self.library_data["characters"][char] = {"templates": []}

        # 提取特征
        features = self.extract_features(char_img)

        # 将图像转换为 base64
        _, buffer = cv2.imencode('.png', char_img)
        img_base64 = base64.b64encode(buffer).decode('utf-8')

        # 创建模板数据
        template_data = {
            "image": img_base64,
            "width": char_img.shape[1],
            "height": char_img.shape[0],
            "features": features,
            "font_info": font_info or {}
        }

        self.library_data["characters"][char]["templates"].append(template_data)
        self.library_data["metadata"]["total_chars"] = len(self.library_data["characters"])

    def create_from_image(self, image_path: str, text_content: str,
                         font_info: Optional[Dict[str, Any]] = None,
                         auto_segment: bool = True) -> Dict[str, Any]:
        """
        从单个图像创建字库

        Args:
            image_path: 图像路径
            text_content: 图像中的文字内容
            font_info: 字体信息
            auto_segment: 是否自动分割

        Returns:
            字库数据
        """
        char_dict = self.extract_characters_from_image(image_path, text_content, auto_segment)

        for char, char_images in char_dict.items():
            for char_img in char_images:
                self.add_character_to_library(char, char_img, font_info)

        return self.library_data

    def create_from_multiple_images(self, image_text_pairs: List[Tuple[str, str]],
                                   font_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从多个图像创建字库

        Args:
            image_text_pairs: [(图像路径, 文字内容), ...]
            font_info: 字体信息

        Returns:
            字库数据
        """
        for image_path, text_content in image_text_pairs:
            try:
                self.create_from_image(image_path, text_content, font_info, auto_segment=True)
            except Exception as e:
                print(f"处理图像 {image_path} 时出错：{e}")

        return self.library_data

    def save_library(self, output_path: str, description: str = "") -> bool:
        """
        保存字库到文件

        Args:
            output_path: 输出文件路径
            description: 字库描述

        Returns:
            是否保存成功
        """
        try:
            self.library_data["metadata"]["description"] = description
            self.library_data["metadata"]["saved_time"] = datetime.now().isoformat()

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.library_data, f, ensure_ascii=False, indent=2)

            print(f"字库已保存到：{output_path}")
            print(f"包含 {self.library_data['metadata']['total_chars']} 个字符")
            return True

        except Exception as e:
            print(f"保存字库失败：{e}")
            return False

    def preview_segmentation(self, image_path: str, output_path: str = None) -> List[Tuple[int, int, int, int]]:
        """
        预览字符分割结果

        Args:
            image_path: 图像路径
            output_path: 预览图像保存路径

        Returns:
            边界框列表 [(x, y, w, h), ...]
        """
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 获取边界框
        bounding_boxes = []
        preview_img = img.copy()

        for i, contour in enumerate(contours):
            x, y, w, h = cv2.boundingRect(contour)
            if w > 5 and h > 5:  # 过滤太小的区域
                bounding_boxes.append((x, y, w, h))
                # 在预览图上绘制边界框
                cv2.rectangle(preview_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(preview_img, str(i), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 保存预览图
        if output_path:
            cv2.imwrite(output_path, preview_img)
            print(f"分割预览图已保存到：{output_path}")

        return bounding_boxes


# ==================== 字库管理功能 ====================

class FontLibraryManager:
    """
    字库管理类
    用于加载、保存、合并和管理字库
    """

    def __init__(self):
        """初始化字库管理器"""
        self.libraries = {}  # 存储多个字库
        self.current_library = None

    def load_library(self, library_path: str, library_name: str = "default") -> bool:
        """
        加载字库文件

        Args:
            library_path: 字库文件路径
            library_name: 字库名称

        Returns:
            是否加载成功
        """
        try:
            with open(library_path, 'r', encoding='utf-8') as f:
                library_data = json.load(f)

            self.libraries[library_name] = library_data
            self.current_library = library_name

            char_count = library_data.get("metadata", {}).get("total_chars", 0)
            print(f"字库 '{library_name}' 加载成功，包含 {char_count} 个字符")
            return True

        except Exception as e:
            print(f"加载字库失败：{e}")
            return False

    def save_library(self, library_name: str, output_path: str) -> bool:
        """
        保存指定字库到文件

        Args:
            library_name: 字库名称
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        if library_name not in self.libraries:
            print(f"字库 '{library_name}' 不存在")
            return False

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.libraries[library_name], f, ensure_ascii=False, indent=2)

            print(f"字库 '{library_name}' 已保存到：{output_path}")
            return True

        except Exception as e:
            print(f"保存字库失败：{e}")
            return False

    def merge_libraries(self, library_names: List[str], merged_name: str = "merged") -> bool:
        """
        合并多个字库

        Args:
            library_names: 要合并的字库名称列表
            merged_name: 合并后的字库名称

        Returns:
            是否合并成功
        """
        if not library_names:
            return False

        # 创建新的合并字库
        merged_library = {
            "version": "1.0",
            "characters": {},
            "metadata": {
                "created_time": datetime.now().isoformat(),
                "total_chars": 0,
                "description": f"合并自: {', '.join(library_names)}"
            }
        }

        # 合并字符数据
        for lib_name in library_names:
            if lib_name not in self.libraries:
                print(f"警告：字库 '{lib_name}' 不存在，跳过")
                continue

            lib_data = self.libraries[lib_name]
            for char, char_data in lib_data.get("characters", {}).items():
                if char not in merged_library["characters"]:
                    merged_library["characters"][char] = {"templates": []}

                # 合并模板
                merged_library["characters"][char]["templates"].extend(
                    char_data.get("templates", [])
                )

        merged_library["metadata"]["total_chars"] = len(merged_library["characters"])
        self.libraries[merged_name] = merged_library

        print(f"已合并 {len(library_names)} 个字库，新字库包含 {merged_library['metadata']['total_chars']} 个字符")
        return True

    def get_character_templates(self, char: str, library_name: str = None) -> List[Dict[str, Any]]:
        """
        获取字符的所有模板

        Args:
            char: 字符
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            模板列表
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return []

        char_data = self.libraries[lib_name].get("characters", {}).get(char, {})
        return char_data.get("templates", [])

    def list_characters(self, library_name: str = None) -> List[str]:
        """
        列出字库中的所有字符

        Args:
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            字符列表
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return []

        return list(self.libraries[lib_name].get("characters", {}).keys())

    def get_library_info(self, library_name: str = None) -> Dict[str, Any]:
        """
        获取字库信息

        Args:
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            字库信息
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return {}

        return self.libraries[lib_name].get("metadata", {})

    def optimize_library(self, library_name: str = None,
                        remove_duplicates: bool = True,
                        similarity_threshold: float = 0.95) -> bool:
        """
        优化字库（去重、压缩等）

        Args:
            library_name: 字库名称，None 表示使用当前字库
            remove_duplicates: 是否移除重复模板
            similarity_threshold: 相似度阈值，超过此值认为是重复

        Returns:
            是否优化成功
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            print(f"字库 '{lib_name}' 不存在")
            return False

        library = self.libraries[lib_name]
        original_count = 0
        optimized_count = 0

        # 统计原始模板数量
        for char_data in library.get("characters", {}).values():
            original_count += len(char_data.get("templates", []))

        if remove_duplicates:
            # 对每个字符的模板进行去重
            for char, char_data in library.get("characters", {}).items():
                templates = char_data.get("templates", [])
                if len(templates) <= 1:
                    continue

                # 去重处理
                unique_templates = self._remove_duplicate_templates(templates, similarity_threshold)
                char_data["templates"] = unique_templates

                print(f"字符 '{char}': {len(templates)} -> {len(unique_templates)} 个模板")

        # 统计优化后模板数量
        for char_data in library.get("characters", {}).values():
            optimized_count += len(char_data.get("templates", []))

        # 更新元数据
        library["metadata"]["optimized_time"] = datetime.now().isoformat()
        library["metadata"]["optimization_info"] = {
            "original_templates": original_count,
            "optimized_templates": optimized_count,
            "reduction_rate": (original_count - optimized_count) / original_count if original_count > 0 else 0
        }

        print(f"字库优化完成：{original_count} -> {optimized_count} 个模板")
        return True

    def _remove_duplicate_templates(self, templates: List[Dict[str, Any]],
                                   threshold: float = 0.95) -> List[Dict[str, Any]]:
        """
        移除重复的模板

        Args:
            templates: 模板列表
            threshold: 相似度阈值

        Returns:
            去重后的模板列表
        """
        if len(templates) <= 1:
            return templates

        unique_templates = []

        for template in templates:
            is_duplicate = False

            # 解码当前模板图像
            try:
                img_data = base64.b64decode(template["image"])
                img_array = np.frombuffer(img_data, np.uint8)
                current_img = cv2.imdecode(img_array, cv2.IMREAD_GRAYSCALE)

                if current_img is None:
                    continue

                # 与已有的唯一模板比较
                for unique_template in unique_templates:
                    unique_img_data = base64.b64decode(unique_template["image"])
                    unique_img_array = np.frombuffer(unique_img_data, np.uint8)
                    unique_img = cv2.imdecode(unique_img_array, cv2.IMREAD_GRAYSCALE)

                    if unique_img is None:
                        continue

                    # 计算相似度
                    similarity = self._calculate_template_similarity(current_img, unique_img)

                    if similarity >= threshold:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_templates.append(template)

            except Exception as e:
                print(f"处理模板时出错：{e}")
                continue

        return unique_templates

    def _calculate_template_similarity(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """
        计算两个模板图像的相似度

        Args:
            img1: 图像1
            img2: 图像2

        Returns:
            相似度 (0-1)
        """
        # 调整图像大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 使用模板匹配计算相似度
        result = cv2.matchTemplate(img1, img2, cv2.TM_CCOEFF_NORMED)
        return float(np.max(result))

    def analyze_library(self, library_name: str = None) -> Dict[str, Any]:
        """
        分析字库统计信息

        Args:
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            分析结果
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return {}

        library = self.libraries[lib_name]
        characters = library.get("characters", {})

        analysis = {
            "total_characters": len(characters),
            "total_templates": 0,
            "character_stats": {},
            "template_size_stats": {
                "min_width": float('inf'),
                "max_width": 0,
                "min_height": float('inf'),
                "max_height": 0,
                "avg_width": 0,
                "avg_height": 0
            }
        }

        total_width = 0
        total_height = 0

        # 分析每个字符
        for char, char_data in characters.items():
            templates = char_data.get("templates", [])
            template_count = len(templates)
            analysis["total_templates"] += template_count

            analysis["character_stats"][char] = {
                "template_count": template_count,
                "sizes": []
            }

            # 分析模板尺寸
            for template in templates:
                width = template.get("width", 0)
                height = template.get("height", 0)

                analysis["character_stats"][char]["sizes"].append((width, height))

                # 更新统计信息
                analysis["template_size_stats"]["min_width"] = min(analysis["template_size_stats"]["min_width"], width)
                analysis["template_size_stats"]["max_width"] = max(analysis["template_size_stats"]["max_width"], width)
                analysis["template_size_stats"]["min_height"] = min(analysis["template_size_stats"]["min_height"], height)
                analysis["template_size_stats"]["max_height"] = max(analysis["template_size_stats"]["max_height"], height)

                total_width += width
                total_height += height

        # 计算平均尺寸
        if analysis["total_templates"] > 0:
            analysis["template_size_stats"]["avg_width"] = total_width / analysis["total_templates"]
            analysis["template_size_stats"]["avg_height"] = total_height / analysis["total_templates"]

        # 处理无限值
        if analysis["template_size_stats"]["min_width"] == float('inf'):
            analysis["template_size_stats"]["min_width"] = 0
        if analysis["template_size_stats"]["min_height"] == float('inf'):
            analysis["template_size_stats"]["min_height"] = 0

        return analysis


# ==================== 使用示例和测试 ====================

def demo_basic_functions():
    """基础功能演示"""
    print("=== 图色脚本功能演示 ===")

    # 创建脚本实例
    script = ImageColorScript()

    print("1. 截图功能演示")
    # 全屏截图
    if script.save_screenshot("full_screen.png"):
        print("   ✓ 全屏截图保存成功：full_screen.png")

    # 区域截图 (左上角 500x300 区域)
    if script.save_screenshot("region_screen.png", region=(0, 0, 500, 300)):
        print("   ✓ 区域截图保存成功：region_screen.png")

    print("\n2. 颜色获取演示")
    # 获取屏幕中心点颜色
    center_x, center_y = script.screen_width // 2, script.screen_height // 2
    color = script.get_pixel_color(center_x, center_y)
    print(f"   屏幕中心点 ({center_x}, {center_y}) 的颜色：{color}")

    print("\n3. 找色功能演示")
    # 查找白色 (255, 255, 255)
    white_positions = script.find_color((255, 255, 255), max_results=5)
    print(f"   找到 {len(white_positions)} 个白色像素点")
    if white_positions:
        print(f"   前几个位置：{white_positions[:3]}")


def demo_advanced_functions():
    """高级功能演示"""
    print("\n=== 高级功能演示 ===")

    script = ImageColorScript()

    print("1. 多点找色演示")
    # 定义一个简单的颜色模式 (例如：查找一个小的白色方块)
    color_pattern = [
        (0, 0, (255, 255, 255)),    # 基准点：白色
        (1, 0, (255, 255, 255)),    # 右边一个像素：白色
        (0, 1, (255, 255, 255)),    # 下边一个像素：白色
        (1, 1, (255, 255, 255)),    # 右下角：白色
    ]

    result = script.find_multi_color(color_pattern, tolerance=10)
    if result:
        print(f"   ✓ 找到颜色模式，位置：{result}")
    else:
        print("   ✗ 未找到指定的颜色模式")

    print("\n2. 等待功能演示")
    print("   注意：等待功能需要实际的图像文件，这里仅演示调用方式")

    print("\n3. 鼠标键盘操作演示")
    print("   注意：以下操作会实际控制鼠标键盘，请小心使用")

    # 获取当前鼠标位置
    current_pos = pyautogui.position()
    print(f"   当前鼠标位置：{current_pos}")


def test_color_functions():
    """测试颜色相关功能"""
    print("\n=== 颜色功能测试 ===")

    script = ImageColorScript()

    # 测试颜色距离计算
    color1 = (255, 0, 0)    # 红色
    color2 = (0, 255, 0)    # 绿色
    color3 = (255, 10, 10)  # 接近红色

    distance1 = script.color_distance(color1, color2)
    distance2 = script.color_distance(color1, color3)

    print(f"红色与绿色的距离：{distance1:.2f}")
    print(f"红色与接近红色的距离：{distance2:.2f}")

    # 测试在小区域内查找颜色
    region = (0, 0, 100, 100)  # 左上角 100x100 区域
    colors_found = script.find_color((255, 255, 255), region=region, max_results=10)
    print(f"在区域 {region} 内找到 {len(colors_found)} 个白色像素")


def demo_font_library_creation():
    """字库制作功能演示"""
    print("\n=== 字库制作功能演示 ===")

    try:
        # 创建字库制作器
        creator = FontLibraryCreator()

        print("1. 字库制作演示")
        print("   注意：需要准备包含文字的图像文件进行测试")

        # 示例：从图像创建字库（需要实际的图像文件）
        # creator.create_from_image("sample_text.png", "示例文字")
        # creator.save_library("sample_font.json", "示例字库")

        print("   字库制作功能已就绪，可使用以下方法：")
        print("   • create_from_image() - 从单个图像创建字库")
        print("   • create_from_multiple_images() - 从多个图像创建字库")
        print("   • preview_segmentation() - 预览字符分割结果")
        print("   • save_library() - 保存字库文件")

    except Exception as e:
        print(f"字库制作演示出错：{e}")


def demo_font_library_management():
    """字库管理功能演示"""
    print("\n=== 字库管理功能演示 ===")

    try:
        # 创建字库管理器
        manager = FontLibraryManager()

        print("1. 字库管理演示")
        print("   字库管理功能已就绪，可使用以下方法：")
        print("   • load_library() - 加载字库文件")
        print("   • save_library() - 保存字库文件")
        print("   • merge_libraries() - 合并多个字库")
        print("   • optimize_library() - 优化字库（去重等）")
        print("   • analyze_library() - 分析字库统计信息")
        print("   • list_characters() - 列出字库中的字符")

        # 示例：分析字库（如果有字库文件的话）
        # manager.load_library("sample_font.json")
        # analysis = manager.analyze_library()
        # print(f"   字库分析结果：{analysis}")

    except Exception as e:
        print(f"字库管理演示出错：{e}")


def demo_text_recognition():
    """文字识别功能演示"""
    print("\n=== 文字识别功能演示 ===")

    try:
        # 创建图色脚本实例
        script = ImageColorScript()

        print("1. 文字识别演示")
        print("   注意：需要先加载字库文件才能进行文字识别")

        # 示例：加载字库并查找文字（需要实际的字库文件）
        # script.load_font_library("sample_font.json")
        # positions = script.find_character("字")
        # text_positions = script.find_text("示例文字")

        print("   文字识别功能已就绪，可使用以下方法：")
        print("   • load_font_library() - 加载字库文件")
        print("   • find_character() - 查找单个字符")
        print("   • find_character_center() - 查找字符中心坐标")
        print("   • find_text() - 查找文字串")
        print("   • find_text_center() - 查找文字串中心坐标")

    except Exception as e:
        print(f"文字识别演示出错：{e}")


def create_sample_font_library():
    """创建示例字库（用于测试）"""
    print("\n=== 创建示例字库 ===")

    try:
        # 创建一个简单的示例字库
        creator = FontLibraryCreator()

        # 创建一些简单的字符图像（用于演示）
        import numpy as np

        # 创建简单的数字字符图像
        for digit in "0123456789":
            # 创建一个简单的数字图像（实际应用中应该从真实图像提取）
            char_img = np.zeros((20, 15), dtype=np.uint8)

            # 简单绘制数字轮廓（这里只是示例，实际应该使用真实的字符图像）
            if digit == "0":
                cv2.rectangle(char_img, (2, 2), (12, 17), 255, 2)
            elif digit == "1":
                cv2.line(char_img, (7, 2), (7, 17), 255, 2)
            # ... 可以添加更多数字的绘制逻辑

            creator.add_character_to_library(digit, char_img, {"type": "digit", "size": "small"})

        # 保存示例字库
        if creator.save_library("sample_digits.json", "示例数字字库"):
            print("   ✓ 示例数字字库创建成功：sample_digits.json")
            return True

    except Exception as e:
        print(f"创建示例字库失败：{e}")
        return False


def test_complete_workflow():
    """完整工作流程测试"""
    print("\n=== 完整工作流程测试 ===")

    try:
        # 1. 创建示例字库
        print("1. 创建示例字库...")
        if not create_sample_font_library():
            print("   示例字库创建失败，跳过后续测试")
            return

        # 2. 测试字库管理
        print("\n2. 测试字库管理...")
        manager = FontLibraryManager()

        if manager.load_library("sample_digits.json", "digits"):
            print("   ✓ 字库加载成功")

            # 分析字库
            analysis = manager.analyze_library("digits")
            print(f"   字库包含 {analysis.get('total_characters', 0)} 个字符")
            print(f"   总共 {analysis.get('total_templates', 0)} 个模板")

            # 列出字符
            characters = manager.list_characters("digits")
            print(f"   字符列表：{characters}")

        # 3. 测试文字识别
        print("\n3. 测试文字识别...")
        script = ImageColorScript()

        if script.load_font_library("sample_digits.json", "digits"):
            print("   ✓ 字库加载到识别引擎成功")
            print("   现在可以使用 find_character() 和 find_text() 方法")

            # 注意：实际的文字识别需要屏幕上有对应的文字
            print("   提示：在屏幕上显示数字，然后调用以下方法进行识别：")
            print("   positions = script.find_character('1')")
            print("   text_pos = script.find_text('123')")

        print("\n✓ 完整工作流程测试完成")

    except Exception as e:
        print(f"完整工作流程测试失败：{e}")


def main():
    """主函数 - 运行所有演示"""
    try:
        # 原有功能演示
        demo_basic_functions()
        demo_advanced_functions()
        test_color_functions()

        # 新增字库功能演示
        demo_font_library_creation()
        demo_font_library_management()
        demo_text_recognition()
        test_complete_workflow()

        print("\n=== 演示完成 ===")
        print("所有功能演示已完成！")
        print("\n可用的主要功能：")
        print("• capture_screen() - 截图")
        print("• save_screenshot() - 保存截图")
        print("• find_image() - 找图")
        print("• find_color() - 找色")
        print("• find_multi_color() - 多点找色")
        print("• get_pixel_color() - 获取像素颜色")
        print("• click() - 鼠标点击")
        print("• type_text() - 输入文本")
        print("• wait_for_image() - 等待图像")
        print("• compare_images() - 图像比较")
        print("• find_image_in_image() - 在大图中查找小图")

        print("\n新增字库功能：")
        print("• FontLibraryCreator - 字库制作")
        print("• FontLibraryManager - 字库管理")
        print("• find_character() - 查找字符")
        print("• find_text() - 查找文字串")
        print("• load_font_library() - 加载字库")

    except Exception as e:
        print(f"演示过程中出现错误：{e}")
        print("请确保已安装所需的依赖库：")
        print("pip install opencv-python numpy mss pyautogui")


# ==================== UI界面功能 ====================
# UI界面已移至独立文件 font_library_ui.py


if __name__ == "__main__":
    # 可以选择运行UI界面或命令行演示
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--ui":
        # 运行UI界面
        try:
            from font_library_ui import FontLibraryUI
            app = FontLibraryUI()
            app.run()
        except ImportError:
            print("UI模块未找到，请确保font_library_ui.py文件存在")
        except Exception as e:
            print(f"启动UI失败: {e}")
    else:
        # 运行命令行演示
        main()
                                ('disabled', '#E5E7EB')])

        # Warning button style (white background, orange text and border)
        self.style.configure("Warning.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#F59E0B',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Warning.TButton",
                     background=[('active', '#FFFBEB'),
                               ('pressed', '#FEF3C7'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#F59E0B'),
                                ('!focus', '#F59E0B'),
                                ('disabled', '#E5E7EB')])

        # Danger button style (white background, red text and border)
        self.style.configure("Danger.TButton",
                           padding=(12, 8),
                           relief="solid",
                           background='white',
                           foreground='#EF4444',
                           borderwidth=1,
                           focuscolor='none',
                           font=('Segoe UI', 9))

        self.style.map("Danger.TButton",
                     background=[('active', '#FEF2F2'),
                               ('pressed', '#FECACA'),
                               ('disabled', '#F9FAFB')],
                     foreground=[('disabled', '#D1D5DB')],
                     bordercolor=[('focus', '#EF4444'),
                                ('!focus', '#EF4444'),
                                ('disabled', '#E5E7EB')])

    def create_main_interface(self):
        """创建主界面"""
        # 创建主容器
        main_frame = ttk.Frame(self.root, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 创建标题
        title_label = ttk.Label(main_frame, text="🔤 字库制作工具",
                               font=('Segoe UI', 18, 'bold'))
        title_label.pack(pady=(0, 20))

        # 创建导航栏
        self.create_navigation(main_frame)

        # 创建主内容区域
        self.content_frame = ttk.Frame(main_frame)
        self.content_frame.pack(fill=tk.BOTH, expand=True, pady=(20, 0))

        # 创建状态栏
        self.create_status_bar(main_frame)

        # 默认显示制作字库界面
        self.show_create_module()

    def create_navigation(self, parent):
        """创建导航栏"""
        nav_frame = ttk.Frame(parent)
        nav_frame.pack(fill=tk.X, pady=(0, 10))

        # 导航按钮
        nav_buttons = [
            ("📝 制作字库", "create", "Primary.TButton"),
            ("📚 管理字库", "manage", "Success.TButton"),
            ("🔍 屏幕找字", "search", "Warning.TButton")
        ]

        for text, module, style in nav_buttons:
            btn = ttk.Button(nav_frame, text=text, style=style,
                           command=lambda m=module: self.switch_module(m))
            btn.pack(side=tk.LEFT, padx=(0, 10))

    def create_status_bar(self, parent):
        """创建状态栏"""
        status_frame = ttk.Frame(parent)
        status_frame.pack(fill=tk.X, side=tk.BOTTOM, pady=(10, 0))

        # 状态标签
        self.status_label = ttk.Label(status_frame, text="就绪",
                                     font=('Segoe UI', 9))
        self.status_label.pack(side=tk.LEFT)

        # 进度条
        self.progress_bar = ttk.Progressbar(status_frame, mode='indeterminate')
        self.progress_bar.pack(side=tk.RIGHT, padx=(10, 0))

    def switch_module(self, module):
        """切换功能模块"""
        if self.current_module == module:
            return

        self.current_module = module

        # 清空当前内容
        for widget in self.content_frame.winfo_children():
            widget.destroy()

        # 显示对应模块
        if module == "create":
            self.show_create_module()
        elif module == "manage":
            self.show_manage_module()
        elif module == "search":
            self.show_search_module()

    def update_status(self, message, show_progress=False):
        """更新状态栏"""
        self.status_label.config(text=message)
        if show_progress:
            self.progress_bar.start()
        else:
            self.progress_bar.stop()
        self.root.update_idletasks()

    # ==================== 制作字库模块 ====================

    def show_create_module(self):
        """显示制作字库模块"""
        # 创建主容器
        create_frame = ttk.Frame(self.content_frame)
        create_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(create_frame, text="字库制作设置", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 图像文件选择
        file_frame = ttk.Frame(left_frame)
        file_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(file_frame, text="选择图像文件:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        file_select_frame = ttk.Frame(file_frame)
        file_select_frame.pack(fill=tk.X, pady=(5, 0))

        self.image_path_var = tk.StringVar()
        self.image_path_entry = ttk.Entry(file_select_frame, textvariable=self.image_path_var,
                                         state='readonly')
        self.image_path_entry.pack(side=tk.LEFT, fill=tk.X, expand=True)

        ttk.Button(file_select_frame, text="浏览", style="Plain.TButton",
                  command=self.select_image_file).pack(side=tk.RIGHT, padx=(5, 0))

        # 文字内容输入
        text_frame = ttk.Frame(left_frame)
        text_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(text_frame, text="图像中的文字内容:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.text_content_var = tk.StringVar()
        self.text_content_entry = ttk.Entry(text_frame, textvariable=self.text_content_var)
        self.text_content_entry.pack(fill=tk.X, pady=(5, 0))

        # 字库信息设置
        info_frame = ttk.Frame(left_frame)
        info_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(info_frame, text="字库信息:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        # 字库名称
        name_frame = ttk.Frame(info_frame)
        name_frame.pack(fill=tk.X, pady=(5, 5))
        ttk.Label(name_frame, text="名称:").pack(side=tk.LEFT)
        self.library_name_var = tk.StringVar(value="新字库")
        ttk.Entry(name_frame, textvariable=self.library_name_var, width=20).pack(side=tk.RIGHT)

        # 字库描述
        desc_frame = ttk.Frame(info_frame)
        desc_frame.pack(fill=tk.X, pady=(0, 5))
        ttk.Label(desc_frame, text="描述:").pack(side=tk.LEFT)
        self.library_desc_var = tk.StringVar()
        ttk.Entry(desc_frame, textvariable=self.library_desc_var, width=20).pack(side=tk.RIGHT)

        # 操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(button_frame, text="预览分割", style="Primary.TButton",
                  command=self.preview_segmentation).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="制作字库", style="Success.TButton",
                  command=self.create_font_library).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="保存字库", style="Warning.TButton",
                  command=self.save_font_library).pack(fill=tk.X)

        # 右侧预览区域
        right_frame = ttk.LabelFrame(create_frame, text="预览区域", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建滚动区域
        canvas = tk.Canvas(right_frame, bg='white')
        scrollbar_v = ttk.Scrollbar(right_frame, orient=tk.VERTICAL, command=canvas.yview)
        scrollbar_h = ttk.Scrollbar(right_frame, orient=tk.HORIZONTAL, command=canvas.xview)

        self.preview_frame = ttk.Frame(canvas)

        canvas.configure(yscrollcommand=scrollbar_v.set, xscrollcommand=scrollbar_h.set)
        canvas.create_window((0, 0), window=self.preview_frame, anchor=tk.NW)

        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_v.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_h.pack(side=tk.BOTTOM, fill=tk.X)

        # 绑定滚动事件
        def configure_scroll_region(event):
            canvas.configure(scrollregion=canvas.bbox("all"))

        self.preview_frame.bind("<Configure>", configure_scroll_region)

        # 初始显示提示
        self.show_preview_hint()

    def show_preview_hint(self):
        """显示预览提示"""
        hint_label = ttk.Label(self.preview_frame,
                              text="请选择图像文件并输入文字内容，然后点击'预览分割'查看字符分割效果",
                              font=('Segoe UI', 11),
                              foreground='#6B7280')
        hint_label.pack(expand=True, pady=50)

    def select_image_file(self):
        """选择图像文件"""
        file_path = filedialog.askopenfilename(
            title="选择图像文件",
            filetypes=[
                ("图像文件", "*.png *.jpg *.jpeg *.bmp *.tiff"),
                ("PNG文件", "*.png"),
                ("JPEG文件", "*.jpg *.jpeg"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            self.image_path_var.set(file_path)
            self.update_status(f"已选择图像: {os.path.basename(file_path)}")

    def preview_segmentation(self):
        """预览字符分割"""
        image_path = self.image_path_var.get()
        text_content = self.text_content_var.get().strip()

        if not image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if not text_content:
            messagebox.showwarning("警告", "请输入图像中的文字内容")
            return

        try:
            self.update_status("正在分割字符...", True)

            # 清空预览区域
            for widget in self.preview_frame.winfo_children():
                widget.destroy()

            # 在后台线程中处理
            def process_segmentation():
                try:
                    # 获取字符分割结果
                    char_dict = self.font_creator.extract_characters_from_image(
                        image_path, text_content, auto_segment=True
                    )

                    # 在主线程中更新UI
                    self.root.after(0, lambda: self.display_segmentation_result(char_dict))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("字符分割失败", str(e)))

            threading.Thread(target=process_segmentation, daemon=True).start()

        except Exception as e:
            self.handle_error("预览分割失败", str(e))

    def display_segmentation_result(self, char_dict):
        """显示分割结果"""
        try:
            self.update_status(f"分割完成，共识别到 {len(char_dict)} 个字符")

            # 创建结果显示区域
            result_frame = ttk.Frame(self.preview_frame)
            result_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # 标题
            title_label = ttk.Label(result_frame, text="字符分割结果",
                                   font=('Segoe UI', 12, 'bold'))
            title_label.pack(pady=(0, 10))

            # 字符网格显示
            chars_frame = ttk.Frame(result_frame)
            chars_frame.pack(fill=tk.BOTH, expand=True)

            row = 0
            col = 0
            max_cols = 8  # 每行最多显示8个字符

            for char, char_images in char_dict.items():
                if char_images:  # 确保有图像数据
                    char_img = char_images[0]  # 取第一个图像

                    # 创建字符容器
                    char_container = ttk.Frame(chars_frame, relief=tk.RIDGE, borderwidth=1)
                    char_container.grid(row=row, column=col, padx=5, pady=5, sticky="nsew")

                    # 显示字符图像
                    try:
                        # 调整图像大小用于显示
                        display_img = cv2.resize(char_img, (60, 60))
                        # 转换为PIL图像
                        pil_img = Image.fromarray(display_img)
                        photo = ImageTk.PhotoImage(pil_img)

                        img_label = ttk.Label(char_container, image=photo)
                        img_label.image = photo  # 保持引用
                        img_label.pack(pady=5)

                    except Exception as e:
                        # 如果图像显示失败，显示占位符
                        placeholder_label = ttk.Label(char_container, text="[图像]",
                                                     width=8, height=4, relief=tk.SUNKEN)
                        placeholder_label.pack(pady=5)

                    # 显示字符文本
                    char_label = ttk.Label(char_container, text=f"'{char}'",
                                          font=('Segoe UI', 10, 'bold'))
                    char_label.pack()

                    col += 1
                    if col >= max_cols:
                        col = 0
                        row += 1

            # 配置网格权重
            for i in range(max_cols):
                chars_frame.grid_columnconfigure(i, weight=1)

        except Exception as e:
            self.handle_error("显示分割结果失败", str(e))

    def create_font_library(self):
        """制作字库"""
        image_path = self.image_path_var.get()
        text_content = self.text_content_var.get().strip()

        if not image_path:
            messagebox.showwarning("警告", "请先选择图像文件")
            return

        if not text_content:
            messagebox.showwarning("警告", "请输入图像中的文字内容")
            return

        try:
            self.update_status("正在制作字库...", True)

            def process_creation():
                try:
                    # 获取字库信息
                    font_info = {
                        "name": self.library_name_var.get(),
                        "description": self.library_desc_var.get(),
                        "source_image": os.path.basename(image_path)
                    }

                    # 创建字库
                    library_data = self.font_creator.create_from_image(
                        image_path, text_content, font_info, auto_segment=True
                    )

                    # 更新元数据
                    self.font_creator.library_data["metadata"]["description"] = font_info["description"]

                    self.root.after(0, lambda: self.on_library_created(library_data))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("制作字库失败", str(e)))

            threading.Thread(target=process_creation, daemon=True).start()

        except Exception as e:
            self.handle_error("制作字库失败", str(e))

    def on_library_created(self, library_data):
        """字库创建完成回调"""
        char_count = library_data.get("metadata", {}).get("total_chars", 0)
        self.update_status(f"字库制作完成，包含 {char_count} 个字符")
        messagebox.showinfo("成功", f"字库制作完成！\n包含 {char_count} 个字符")

    def save_font_library(self):
        """保存字库"""
        if not self.font_creator.library_data.get("characters"):
            messagebox.showwarning("警告", "请先制作字库")
            return

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存字库文件",
            defaultextension=".json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                library_name = self.library_name_var.get() or "字库"
                if self.font_creator.save_library(file_path, library_name):
                    self.update_status(f"字库已保存: {os.path.basename(file_path)}")
                    messagebox.showinfo("成功", f"字库已保存到:\n{file_path}")
                else:
                    messagebox.showerror("错误", "保存字库失败")
            except Exception as e:
                self.handle_error("保存字库失败", str(e))

    def handle_error(self, title, message):
        """处理错误"""
        self.update_status("操作失败")
        messagebox.showerror(title, message)
        print(f"错误: {title} - {message}")  # 同时输出到控制台用于调试

    # ==================== 管理字库模块 ====================

    def show_manage_module(self):
        """显示管理字库模块"""
        # 创建主容器
        manage_frame = ttk.Frame(self.content_frame)
        manage_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧字库列表
        left_frame = ttk.LabelFrame(manage_frame, text="字库列表", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 字库加载按钮
        load_frame = ttk.Frame(left_frame)
        load_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Button(load_frame, text="📁 加载字库", style="Primary.TButton",
                  command=self.load_font_library).pack(fill=tk.X)

        # 字库列表
        list_frame = ttk.Frame(left_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 创建Treeview显示字库列表
        columns = ("name", "chars", "status")
        self.library_tree = ttk.Treeview(list_frame, columns=columns, show="headings", height=12)

        # 设置列标题
        self.library_tree.heading("name", text="字库名称")
        self.library_tree.heading("chars", text="字符数")
        self.library_tree.heading("status", text="状态")

        # 设置列宽
        self.library_tree.column("name", width=150)
        self.library_tree.column("chars", width=80)
        self.library_tree.column("status", width=80)

        # 添加滚动条
        tree_scroll = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.library_tree.yview)
        self.library_tree.configure(yscrollcommand=tree_scroll.set)

        self.library_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        tree_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定选择事件
        self.library_tree.bind("<<TreeviewSelect>>", self.on_library_select)

        # 字库操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="🔄 优化字库", style="Success.TButton",
                  command=self.optimize_library).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="🔗 合并字库", style="Warning.TButton",
                  command=self.merge_libraries).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="💾 保存字库", style="Plain.TButton",
                  command=self.save_selected_library).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="🗑️ 删除字库", style="Danger.TButton",
                  command=self.remove_library).pack(fill=tk.X)

        # 右侧详情面板
        right_frame = ttk.LabelFrame(manage_frame, text="字库详情", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建详情显示区域
        self.create_library_details_panel(right_frame)

        # 初始化显示
        self.refresh_library_list()

    def create_library_details_panel(self, parent):
        """创建字库详情面板"""
        # 基本信息区域
        info_frame = ttk.LabelFrame(parent, text="基本信息", padding=10)
        info_frame.pack(fill=tk.X, pady=(0, 10))

        # 创建信息显示标签
        self.info_labels = {}
        info_items = [
            ("name", "字库名称:"),
            ("chars", "字符总数:"),
            ("templates", "模板总数:"),
            ("created", "创建时间:"),
            ("description", "描述:")
        ]

        for key, label_text in info_items:
            frame = ttk.Frame(info_frame)
            frame.pack(fill=tk.X, pady=2)

            ttk.Label(frame, text=label_text, font=('Segoe UI', 9, 'bold')).pack(side=tk.LEFT)
            self.info_labels[key] = ttk.Label(frame, text="未选择", foreground='#6B7280')
            self.info_labels[key].pack(side=tk.LEFT, padx=(10, 0))

        # 字符列表区域
        chars_frame = ttk.LabelFrame(parent, text="字符列表", padding=10)
        chars_frame.pack(fill=tk.BOTH, expand=True)

        # 创建字符显示区域
        chars_canvas = tk.Canvas(chars_frame, bg='white', height=200)
        chars_scroll = ttk.Scrollbar(chars_frame, orient=tk.VERTICAL, command=chars_canvas.yview)

        self.chars_display_frame = ttk.Frame(chars_canvas)

        chars_canvas.configure(yscrollcommand=chars_scroll.set)
        chars_canvas.create_window((0, 0), window=self.chars_display_frame, anchor=tk.NW)

        chars_canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        chars_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定滚动事件
        def configure_chars_scroll(event):
            chars_canvas.configure(scrollregion=chars_canvas.bbox("all"))

        self.chars_display_frame.bind("<Configure>", configure_chars_scroll)

        # 初始显示提示
        self.show_details_hint()

    def show_details_hint(self):
        """显示详情提示"""
        hint_label = ttk.Label(self.chars_display_frame,
                              text="请从左侧列表选择字库查看详细信息",
                              font=('Segoe UI', 11),
                              foreground='#6B7280')
        hint_label.pack(expand=True, pady=30)

    def load_font_library(self):
        """加载字库文件"""
        file_path = filedialog.askopenfilename(
            title="选择字库文件",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                self.update_status("正在加载字库...", True)

                # 生成字库名称
                base_name = os.path.splitext(os.path.basename(file_path))[0]
                library_name = base_name
                counter = 1

                # 确保名称唯一
                while library_name in self.loaded_libraries:
                    library_name = f"{base_name}_{counter}"
                    counter += 1

                # 加载字库
                if self.font_manager.load_library(file_path, library_name):
                    # 保存到本地管理
                    self.loaded_libraries[library_name] = {
                        "path": file_path,
                        "data": self.font_manager.libraries[library_name]
                    }

                    self.refresh_library_list()
                    self.update_status(f"字库加载成功: {library_name}")
                    messagebox.showinfo("成功", f"字库 '{library_name}' 加载成功")
                else:
                    messagebox.showerror("错误", "字库加载失败")

            except Exception as e:
                self.handle_error("加载字库失败", str(e))

    def refresh_library_list(self):
        """刷新字库列表"""
        # 清空现有项目
        for item in self.library_tree.get_children():
            self.library_tree.delete(item)

        # 添加已加载的字库
        for name, info in self.loaded_libraries.items():
            data = info["data"]
            char_count = data.get("metadata", {}).get("total_chars", 0)
            status = "已加载"

            self.library_tree.insert("", tk.END, values=(name, char_count, status))

    def on_library_select(self, event):
        """字库选择事件"""
        selection = self.library_tree.selection()
        if not selection:
            return

        item = self.library_tree.item(selection[0])
        library_name = item["values"][0]

        if library_name in self.loaded_libraries:
            self.show_library_details(library_name)

    def show_library_details(self, library_name):
        """显示字库详情"""
        try:
            library_data = self.loaded_libraries[library_name]["data"]
            metadata = library_data.get("metadata", {})
            characters = library_data.get("characters", {})

            # 更新基本信息
            self.info_labels["name"].config(text=library_name)
            self.info_labels["chars"].config(text=str(len(characters)))

            # 计算模板总数
            total_templates = sum(len(char_data.get("templates", []))
                                for char_data in characters.values())
            self.info_labels["templates"].config(text=str(total_templates))

            # 创建时间
            created_time = metadata.get("created_time", "未知")
            if created_time != "未知":
                try:
                    from datetime import datetime
                    dt = datetime.fromisoformat(created_time.replace('Z', '+00:00'))
                    created_time = dt.strftime("%Y-%m-%d %H:%M")
                except:
                    pass
            self.info_labels["created"].config(text=created_time)

            # 描述
            description = metadata.get("description", "无描述")
            self.info_labels["description"].config(text=description)

            # 显示字符列表
            self.show_character_list(characters)

        except Exception as e:
            self.handle_error("显示字库详情失败", str(e))

    def show_character_list(self, characters):
        """显示字符列表"""
        # 清空现有字符显示
        for widget in self.chars_display_frame.winfo_children():
            widget.destroy()

        if not characters:
            hint_label = ttk.Label(self.chars_display_frame,
                                  text="该字库中没有字符",
                                  font=('Segoe UI', 11),
                                  foreground='#6B7280')
            hint_label.pack(expand=True, pady=30)
            return

        # 创建字符网格
        chars_grid = ttk.Frame(self.chars_display_frame)
        chars_grid.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # 按行显示字符
        chars_per_row = 10
        row = 0
        col = 0

        for char in sorted(characters.keys()):
            char_label = ttk.Label(chars_grid, text=f"'{char}'",
                                  font=('Segoe UI', 10),
                                  relief=tk.RIDGE, borderwidth=1,
                                  padding=5)
            char_label.grid(row=row, column=col, padx=2, pady=2, sticky="ew")

            col += 1
            if col >= chars_per_row:
                col = 0
                row += 1

        # 配置网格权重
        for i in range(min(chars_per_row, len(characters))):
            chars_grid.grid_columnconfigure(i, weight=1)

    def optimize_library(self):
        """优化选中的字库"""
        selection = self.library_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要优化的字库")
            return

        item = self.library_tree.item(selection[0])
        library_name = item["values"][0]

        try:
            self.update_status("正在优化字库...", True)

            def process_optimization():
                try:
                    # 优化字库
                    result = self.font_manager.optimize_library(library_name)

                    self.root.after(0, lambda: self.on_optimization_complete(library_name, result))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("优化字库失败", str(e)))

            threading.Thread(target=process_optimization, daemon=True).start()

        except Exception as e:
            self.handle_error("优化字库失败", str(e))

    def on_optimization_complete(self, library_name, result):
        """优化完成回调"""
        removed_count = result.get("removed_duplicates", 0)
        self.update_status(f"字库优化完成，移除了 {removed_count} 个重复项")
        messagebox.showinfo("成功", f"字库 '{library_name}' 优化完成\n移除了 {removed_count} 个重复项")

        # 刷新显示
        self.refresh_library_list()
        self.show_library_details(library_name)

    def merge_libraries(self):
        """合并字库"""
        if len(self.loaded_libraries) < 2:
            messagebox.showwarning("警告", "至少需要加载2个字库才能进行合并")
            return

        # 创建合并对话框
        self.show_merge_dialog()

    def show_merge_dialog(self):
        """显示合并对话框"""
        dialog = tk.Toplevel(self.root)
        dialog.title("合并字库")
        dialog.geometry("400x300")
        dialog.transient(self.root)
        dialog.grab_set()

        # 主框架
        main_frame = ttk.Frame(dialog, padding=20)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 标题
        ttk.Label(main_frame, text="选择要合并的字库",
                 font=('Segoe UI', 12, 'bold')).pack(pady=(0, 15))

        # 字库选择列表
        list_frame = ttk.Frame(main_frame)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 创建复选框列表
        self.merge_vars = {}
        for library_name in self.loaded_libraries.keys():
            var = tk.BooleanVar()
            self.merge_vars[library_name] = var

            cb = ttk.Checkbutton(list_frame, text=library_name, variable=var)
            cb.pack(anchor=tk.W, pady=2)

        # 合并后名称
        name_frame = ttk.Frame(main_frame)
        name_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(name_frame, text="合并后名称:").pack(side=tk.LEFT)
        self.merge_name_var = tk.StringVar(value="合并字库")
        ttk.Entry(name_frame, textvariable=self.merge_name_var).pack(side=tk.RIGHT, fill=tk.X, expand=True, padx=(10, 0))

        # 按钮
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X)

        ttk.Button(button_frame, text="取消", style="Plain.TButton",
                  command=dialog.destroy).pack(side=tk.RIGHT, padx=(5, 0))

        ttk.Button(button_frame, text="合并", style="Success.TButton",
                  command=lambda: self.execute_merge(dialog)).pack(side=tk.RIGHT)

    def execute_merge(self, dialog):
        """执行合并操作"""
        # 获取选中的字库
        selected_libraries = [name for name, var in self.merge_vars.items() if var.get()]

        if len(selected_libraries) < 2:
            messagebox.showwarning("警告", "请至少选择2个字库进行合并")
            return

        merge_name = self.merge_name_var.get().strip()
        if not merge_name:
            messagebox.showwarning("警告", "请输入合并后的字库名称")
            return

        dialog.destroy()

        try:
            self.update_status("正在合并字库...", True)

            def process_merge():
                try:
                    # 执行合并
                    merged_data = self.font_manager.merge_libraries(selected_libraries, merge_name)

                    self.root.after(0, lambda: self.on_merge_complete(merge_name, merged_data))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("合并字库失败", str(e)))

            threading.Thread(target=process_merge, daemon=True).start()

        except Exception as e:
            self.handle_error("合并字库失败", str(e))

    def on_merge_complete(self, merge_name, merged_data):
        """合并完成回调"""
        # 添加到已加载字库列表
        self.loaded_libraries[merge_name] = {
            "path": None,  # 合并的字库没有文件路径
            "data": merged_data
        }

        char_count = merged_data.get("metadata", {}).get("total_chars", 0)
        self.update_status(f"字库合并完成，包含 {char_count} 个字符")
        messagebox.showinfo("成功", f"字库合并完成！\n新字库 '{merge_name}' 包含 {char_count} 个字符")

        # 刷新显示
        self.refresh_library_list()

    def save_selected_library(self):
        """保存选中的字库"""
        selection = self.library_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要保存的字库")
            return

        item = self.library_tree.item(selection[0])
        library_name = item["values"][0]

        # 选择保存路径
        file_path = filedialog.asksaveasfilename(
            title="保存字库文件",
            defaultextension=".json",
            initialvalue=f"{library_name}.json",
            filetypes=[
                ("JSON文件", "*.json"),
                ("所有文件", "*.*")
            ]
        )

        if file_path:
            try:
                if self.font_manager.save_library(library_name, file_path):
                    self.update_status(f"字库已保存: {os.path.basename(file_path)}")
                    messagebox.showinfo("成功", f"字库已保存到:\n{file_path}")
                else:
                    messagebox.showerror("错误", "保存字库失败")
            except Exception as e:
                self.handle_error("保存字库失败", str(e))

    def remove_library(self):
        """删除选中的字库"""
        selection = self.library_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要删除的字库")
            return

        item = self.library_tree.item(selection[0])
        library_name = item["values"][0]

        # 确认删除
        if messagebox.askyesno("确认删除", f"确定要删除字库 '{library_name}' 吗？\n此操作不可撤销。"):
            try:
                # 从管理器中删除
                if library_name in self.font_manager.libraries:
                    del self.font_manager.libraries[library_name]

                # 从本地列表中删除
                if library_name in self.loaded_libraries:
                    del self.loaded_libraries[library_name]

                self.refresh_library_list()
                self.show_details_hint()  # 显示提示信息

                self.update_status(f"字库 '{library_name}' 已删除")

            except Exception as e:
                self.handle_error("删除字库失败", str(e))

    # ==================== 屏幕找字模块 ====================

    def show_search_module(self):
        """显示屏幕找字模块"""
        # 创建主容器
        search_frame = ttk.Frame(self.content_frame)
        search_frame.pack(fill=tk.BOTH, expand=True)

        # 左侧控制面板
        left_frame = ttk.LabelFrame(search_frame, text="查找设置", padding=15)
        left_frame.pack(side=tk.LEFT, fill=tk.Y, padx=(0, 10))
        left_frame.config(width=350)
        left_frame.pack_propagate(False)

        # 字库选择
        library_frame = ttk.Frame(left_frame)
        library_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(library_frame, text="选择字库:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.search_library_var = tk.StringVar()
        self.library_combo = ttk.Combobox(library_frame, textvariable=self.search_library_var,
                                         state="readonly")
        self.library_combo.pack(fill=tk.X, pady=(5, 0))

        # 查找文字输入
        text_frame = ttk.Frame(left_frame)
        text_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(text_frame, text="查找文字:", font=('Segoe UI', 10, 'bold')).pack(anchor=tk.W)

        self.search_text_var = tk.StringVar()
        self.search_text_entry = ttk.Entry(text_frame, textvariable=self.search_text_var)
        self.search_text_entry.pack(fill=tk.X, pady=(5, 0))

        # 搜索区域设置
        region_frame = ttk.LabelFrame(left_frame, text="搜索区域", padding=10)
        region_frame.pack(fill=tk.X, pady=(0, 15))

        # 全屏或自定义区域选择
        self.region_mode_var = tk.StringVar(value="fullscreen")

        ttk.Radiobutton(region_frame, text="全屏搜索", variable=self.region_mode_var,
                       value="fullscreen", command=self.on_region_mode_change).pack(anchor=tk.W)

        ttk.Radiobutton(region_frame, text="自定义区域", variable=self.region_mode_var,
                       value="custom", command=self.on_region_mode_change).pack(anchor=tk.W, pady=(5, 0))

        # 自定义区域输入
        self.custom_region_frame = ttk.Frame(region_frame)
        self.custom_region_frame.pack(fill=tk.X, pady=(10, 0))

        # 区域坐标输入
        coord_labels = ["X:", "Y:", "宽度:", "高度:"]
        self.region_vars = []

        for i, label in enumerate(coord_labels):
            coord_frame = ttk.Frame(self.custom_region_frame)
            coord_frame.pack(fill=tk.X, pady=2)

            ttk.Label(coord_frame, text=label, width=6).pack(side=tk.LEFT)
            var = tk.StringVar(value="0" if i < 2 else "800" if i == 2 else "600")
            self.region_vars.append(var)
            ttk.Entry(coord_frame, textvariable=var, width=10).pack(side=tk.LEFT, padx=(5, 0))

        # 区域选择按钮
        ttk.Button(self.custom_region_frame, text="选择区域", style="Plain.TButton",
                  command=self.select_search_region).pack(fill=tk.X, pady=(10, 0))

        # 搜索参数设置
        params_frame = ttk.LabelFrame(left_frame, text="搜索参数", padding=10)
        params_frame.pack(fill=tk.X, pady=(0, 15))

        # 相似度阈值
        threshold_frame = ttk.Frame(params_frame)
        threshold_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(threshold_frame, text="相似度:").pack(side=tk.LEFT)
        self.threshold_var = tk.DoubleVar(value=0.8)
        threshold_scale = ttk.Scale(threshold_frame, from_=0.1, to=1.0,
                                   variable=self.threshold_var, orient=tk.HORIZONTAL)
        threshold_scale.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(10, 5))

        self.threshold_label = ttk.Label(threshold_frame, text="0.8")
        self.threshold_label.pack(side=tk.RIGHT)

        # 绑定阈值变化事件
        threshold_scale.configure(command=self.on_threshold_change)

        # 查找模式
        mode_frame = ttk.Frame(params_frame)
        mode_frame.pack(fill=tk.X, pady=(5, 0))

        ttk.Label(mode_frame, text="查找模式:").pack(side=tk.LEFT)
        self.search_mode_var = tk.StringVar(value="first")
        mode_combo = ttk.Combobox(mode_frame, textvariable=self.search_mode_var,
                                 values=["first", "all"], state="readonly", width=10)
        mode_combo.pack(side=tk.RIGHT)

        # 操作按钮
        button_frame = ttk.Frame(left_frame)
        button_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Button(button_frame, text="🔍 开始查找", style="Primary.TButton",
                  command=self.start_search).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="📷 截图预览", style="Success.TButton",
                  command=self.preview_search_area).pack(fill=tk.X, pady=(0, 5))

        ttk.Button(button_frame, text="🎯 点击结果", style="Warning.TButton",
                  command=self.click_search_result).pack(fill=tk.X)

        # 右侧结果显示区域
        right_frame = ttk.LabelFrame(search_frame, text="查找结果", padding=15)
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True)

        # 创建结果显示区域
        self.create_search_results_panel(right_frame)

        # 初始化
        self.update_library_combo()
        self.on_region_mode_change()

    def create_search_results_panel(self, parent):
        """创建查找结果面板"""
        # 结果统计
        stats_frame = ttk.Frame(parent)
        stats_frame.pack(fill=tk.X, pady=(0, 10))

        self.result_stats_label = ttk.Label(stats_frame, text="未开始查找",
                                           font=('Segoe UI', 10, 'bold'))
        self.result_stats_label.pack(side=tk.LEFT)

        # 结果列表
        results_frame = ttk.Frame(parent)
        results_frame.pack(fill=tk.BOTH, expand=True)

        # 创建Treeview显示结果
        columns = ("text", "x", "y", "width", "height", "confidence")
        self.results_tree = ttk.Treeview(results_frame, columns=columns, show="headings", height=15)

        # 设置列标题
        self.results_tree.heading("text", text="文字")
        self.results_tree.heading("x", text="X")
        self.results_tree.heading("y", text="Y")
        self.results_tree.heading("width", text="宽度")
        self.results_tree.heading("height", text="高度")
        self.results_tree.heading("confidence", text="相似度")

        # 设置列宽
        self.results_tree.column("text", width=100)
        self.results_tree.column("x", width=60)
        self.results_tree.column("y", width=60)
        self.results_tree.column("width", width=60)
        self.results_tree.column("height", width=60)
        self.results_tree.column("confidence", width=80)

        # 添加滚动条
        results_scroll = ttk.Scrollbar(results_frame, orient=tk.VERTICAL, command=self.results_tree.yview)
        self.results_tree.configure(yscrollcommand=results_scroll.set)

        self.results_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        results_scroll.pack(side=tk.RIGHT, fill=tk.Y)

        # 绑定双击事件
        self.results_tree.bind("<Double-1>", self.on_result_double_click)

        # 初始显示提示
        self.show_search_hint()

    def show_search_hint(self):
        """显示查找提示"""
        self.result_stats_label.config(text="请选择字库并输入要查找的文字，然后点击'开始查找'")

    def update_library_combo(self):
        """更新字库下拉列表"""
        libraries = list(self.loaded_libraries.keys())
        self.library_combo['values'] = libraries

        if libraries:
            self.library_combo.set(libraries[0])
        else:
            self.library_combo.set("")

    def on_region_mode_change(self):
        """区域模式变化事件"""
        if self.region_mode_var.get() == "fullscreen":
            # 禁用自定义区域输入
            for child in self.custom_region_frame.winfo_children():
                if isinstance(child, ttk.Frame):
                    for widget in child.winfo_children():
                        if isinstance(widget, ttk.Entry):
                            widget.configure(state="disabled")
                elif isinstance(child, ttk.Button):
                    child.configure(state="disabled")
        else:
            # 启用自定义区域输入
            for child in self.custom_region_frame.winfo_children():
                if isinstance(child, ttk.Frame):
                    for widget in child.winfo_children():
                        if isinstance(widget, ttk.Entry):
                            widget.configure(state="normal")
                elif isinstance(child, ttk.Button):
                    child.configure(state="normal")

    def on_threshold_change(self, value):
        """阈值变化事件"""
        self.threshold_label.config(text=f"{float(value):.2f}")

    def select_search_region(self):
        """选择搜索区域"""
        messagebox.showinfo("提示", "请在屏幕上拖拽选择搜索区域\n(此功能需要额外实现区域选择工具)")
        # 这里可以实现一个区域选择工具
        # 暂时使用简单的输入方式

    def start_search(self):
        """开始查找文字"""
        library_name = self.search_library_var.get()
        search_text = self.search_text_var.get().strip()

        if not library_name:
            messagebox.showwarning("警告", "请先选择字库")
            return

        if not search_text:
            messagebox.showwarning("警告", "请输入要查找的文字")
            return

        try:
            self.update_status("正在查找文字...", True)

            # 清空结果
            for item in self.results_tree.get_children():
                self.results_tree.delete(item)

            def process_search():
                try:
                    # 加载字库到图像脚本
                    if library_name in self.loaded_libraries:
                        library_path = self.loaded_libraries[library_name].get("path")
                        if library_path:
                            self.image_script.load_font_library(library_path, library_name)
                        else:
                            # 对于合并的字库，直接设置数据
                            self.image_script.font_manager.libraries[library_name] = \
                                self.loaded_libraries[library_name]["data"]
                            self.image_script.font_manager.current_library = library_name

                    # 获取搜索区域
                    region = self.get_search_region()

                    # 获取搜索参数
                    threshold = self.threshold_var.get()
                    search_mode = self.search_mode_var.get()

                    # 执行搜索
                    if search_mode == "first":
                        # 查找第一个匹配
                        if len(search_text) == 1:
                            # 单个字符
                            result = self.image_script.find_character(search_text, region, threshold)
                            results = [result] if result else []
                        else:
                            # 文字串
                            result = self.image_script.find_text(search_text, region, threshold)
                            results = [result] if result else []
                    else:
                        # 查找所有匹配
                        if len(search_text) == 1:
                            # 单个字符
                            results = self.image_script.find_character(search_text, region, threshold)
                        else:
                            # 文字串
                            results = self.image_script.find_text(search_text, region, threshold)

                    self.root.after(0, lambda: self.on_search_complete(search_text, results))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("查找文字失败", str(e)))

            threading.Thread(target=process_search, daemon=True).start()

        except Exception as e:
            self.handle_error("查找文字失败", str(e))

    def get_search_region(self):
        """获取搜索区域"""
        if self.region_mode_var.get() == "fullscreen":
            return None
        else:
            try:
                x = int(self.region_vars[0].get())
                y = int(self.region_vars[1].get())
                width = int(self.region_vars[2].get())
                height = int(self.region_vars[3].get())
                return (x, y, width, height)
            except ValueError:
                messagebox.showwarning("警告", "区域坐标必须是数字")
                return None

    def on_search_complete(self, search_text, results):
        """搜索完成回调"""
        if results:
            self.result_stats_label.config(text=f"找到 {len(results)} 个匹配项")

            # 显示结果
            for i, result in enumerate(results):
                if isinstance(result, tuple) and len(result) >= 4:
                    x, y, w, h = result[:4]
                    confidence = result[4] if len(result) > 4 else "N/A"

                    self.results_tree.insert("", tk.END, values=(
                        search_text, x, y, w, h, f"{confidence:.3f}" if isinstance(confidence, float) else confidence
                    ))

            self.update_status(f"查找完成，找到 {len(results)} 个匹配项")
        else:
            self.result_stats_label.config(text="未找到匹配项")
            self.update_status("查找完成，未找到匹配项")

    def preview_search_area(self):
        """预览搜索区域"""
        try:
            self.update_status("正在截图...", True)

            def process_screenshot():
                try:
                    region = self.get_search_region()

                    # 截图
                    if region:
                        screenshot = self.image_script.capture_screen(region)
                    else:
                        screenshot = self.image_script.capture_screen()

                    # 保存临时文件
                    temp_path = "temp_preview.png"
                    cv2.imwrite(temp_path, screenshot)

                    self.root.after(0, lambda: self.show_preview_window(temp_path))

                except Exception as e:
                    self.root.after(0, lambda: self.handle_error("截图预览失败", str(e)))

            threading.Thread(target=process_screenshot, daemon=True).start()

        except Exception as e:
            self.handle_error("截图预览失败", str(e))

    def show_preview_window(self, image_path):
        """显示预览窗口"""
        try:
            self.update_status("截图完成")

            # 创建预览窗口
            preview_window = tk.Toplevel(self.root)
            preview_window.title("搜索区域预览")
            preview_window.geometry("800x600")

            # 加载并显示图像
            img = Image.open(image_path)

            # 调整图像大小以适应窗口
            img.thumbnail((750, 550), Image.Resampling.LANCZOS)
            photo = ImageTk.PhotoImage(img)

            # 显示图像
            img_label = ttk.Label(preview_window, image=photo)
            img_label.image = photo  # 保持引用
            img_label.pack(expand=True)

            # 删除临时文件
            try:
                os.remove(image_path)
            except:
                pass

        except Exception as e:
            self.handle_error("显示预览失败", str(e))

    def click_search_result(self):
        """点击选中的搜索结果"""
        selection = self.results_tree.selection()
        if not selection:
            messagebox.showwarning("警告", "请先选择要点击的结果")
            return

        item = self.results_tree.item(selection[0])
        values = item["values"]

        try:
            x, y, w, h = int(values[1]), int(values[2]), int(values[3]), int(values[4])

            # 计算中心点
            center_x = x + w // 2
            center_y = y + h // 2

            # 执行点击
            self.image_script.click(center_x, center_y)

            self.update_status(f"已点击位置: ({center_x}, {center_y})")
            messagebox.showinfo("成功", f"已点击位置: ({center_x}, {center_y})")

        except Exception as e:
            self.handle_error("点击失败", str(e))

    def on_result_double_click(self, event):
        """结果双击事件"""
        self.click_search_result()

    # ==================== 运行方法 ====================

    def run(self):
        """运行UI界面"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.root.quit()
        except Exception as e:
            print(f"UI运行错误: {e}")
            messagebox.showerror("错误", f"UI运行错误: {e}")


if __name__ == "__main__":
    # 可以选择运行UI界面或命令行演示
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--ui":
        # 运行UI界面
        try:
            from font_library_ui import FontLibraryUI
            app = FontLibraryUI()
            app.run()
        except ImportError:
            print("UI模块未找到，请确保font_library_ui.py文件存在")
        except Exception as e:
            print(f"启动UI失败: {e}")
    else:
        # 运行命令行演示
        main()
