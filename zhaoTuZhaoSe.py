import cv2
import numpy as np
import mss
import time
import os
from typing import List, Tuple, Optional, Union, Dict, Any
import pyautogui
import math
import json
import base64
from datetime import datetime
import pickle

class ImageColorScript:
    """
    完整的图色脚本功能类
    包含区域找图、屏幕找图、区域找色、多点找色、截图、区域截图等功能
    """
    
    def __init__(self):
        """初始化图色脚本"""
        self.screen_width, self.screen_height = pyautogui.size()
        # 禁用 pyautogui 的安全检查
        pyautogui.FAILSAFE = False
        # 字库管理器
        self.font_manager = FontLibraryManager()
        
    def capture_screen(self, region: Optional[Tuple[int, int, int, int]] = None) -> np.ndarray:
        """
        截取屏幕图像
        
        Args:
            region: 截图区域 (x, y, width, height)，None 表示全屏
            
        Returns:
            截取的图像数组 (BGR 格式)
        """
        with mss.mss() as sct:
            if region is None:
                # 全屏截图
                monitor = sct.monitors[1]
                img = sct.grab(monitor)
            else:
                # 区域截图
                x, y, width, height = region
                monitor = {"top": y, "left": x, "width": width, "height": height}
                img = sct.grab(monitor)
            
            img_np = np.array(img)
            img_bgr = cv2.cvtColor(img_np, cv2.COLOR_BGRA2BGR)
            return img_bgr
    
    def save_screenshot(self, filename: str, region: Optional[Tuple[int, int, int, int]] = None) -> bool:
        """
        保存截图到文件
        
        Args:
            filename: 保存的文件名
            region: 截图区域 (x, y, width, height)，None 表示全屏
            
        Returns:
            是否保存成功
        """
        try:
            img = self.capture_screen(region)
            cv2.imwrite(filename, img)
            return True
        except Exception as e:
            print(f"保存截图失败：{e}")
            return False
    
    def get_pixel_color(self, x: int, y: int) -> Tuple[int, int, int]:
        """
        获取指定坐标的颜色值
        
        Args:
            x: X 坐标
            y: Y 坐标
            
        Returns:
            颜色值 (B, G, R)
        """
        img = self.capture_screen(region=(x, y, 1, 1))
        return tuple(img[0, 0])
    
    def color_distance(self, color1: Tuple[int, int, int], color2: Tuple[int, int, int]) -> float:
        """
        计算两个颜色之间的距离
        
        Args:
            color1: 颜色 1 (B, G, R)
            color2: 颜色 2 (B, G, R)
            
        Returns:
            颜色距离
        """
        return math.sqrt(sum((a - b) ** 2 for a, b in zip(color1, color2)))
    
    def find_image(self, template_path: str, region: Optional[Tuple[int, int, int, int]] = None, 
                   threshold: float = 0.8, max_results: int = 1) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找图像
        
        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量
            
        Returns:
            找到的位置列表 [(x, y, width, height), ...]
        """
        # 读取模板图像
        template = cv2.imread(template_path, cv2.IMREAD_COLOR)
        if template is None:
            raise FileNotFoundError(f"模板文件 {template_path} 不存在")
        
        # 截取搜索区域
        screen_img = self.capture_screen(region)
        
        # 执行模板匹配
        result = cv2.matchTemplate(screen_img, template, cv2.TM_CCOEFF_NORMED)
        
        # 查找匹配位置
        locations = np.where(result >= threshold)
        h, w = template.shape[:2]
        
        # 获取所有匹配位置
        matches = []
        for pt in zip(*locations[::-1]):
            x, y = pt
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((x, y, w, h))
        
        # 非极大值抑制去重
        if len(matches) > 0:
            matches = self._non_max_suppression(matches)
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_image_center(self, template_path: str, region: Optional[Tuple[int, int, int, int]] = None, 
                         threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        查找图像并返回中心坐标
        
        Args:
            template_path: 模板图像路径
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            
        Returns:
            图像中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_image(template_path, region, threshold, max_results=1)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None
    
    def _non_max_suppression(self, boxes: List[Tuple[int, int, int, int]], 
                           overlap_threshold: float = 0.3) -> List[Tuple[int, int, int, int]]:
        """
        非极大值抑制去重
        
        Args:
            boxes: 边界框列表 [(x, y, w, h), ...]
            overlap_threshold: 重叠阈值
            
        Returns:
            去重后的边界框列表
        """
        if len(boxes) == 0:
            return []
        
        boxes = np.array(boxes)
        pick = []
        
        x1 = boxes[:, 0]
        y1 = boxes[:, 1]
        x2 = boxes[:, 0] + boxes[:, 2]
        y2 = boxes[:, 1] + boxes[:, 3]
        
        area = (x2 - x1 + 1) * (y2 - y1 + 1)
        idxs = np.argsort(y2)
        
        while len(idxs) > 0:
            last = len(idxs) - 1
            i = idxs[last]
            pick.append(i)
            
            xx1 = np.maximum(x1[i], x1[idxs[:last]])
            yy1 = np.maximum(y1[i], y1[idxs[:last]])
            xx2 = np.minimum(x2[i], x2[idxs[:last]])
            yy2 = np.minimum(y2[i], y2[idxs[:last]])
            
            w = np.maximum(0, xx2 - xx1 + 1)
            h = np.maximum(0, yy2 - yy1 + 1)
            
            overlap = (w * h) / area[idxs[:last]]
            
            idxs = np.delete(idxs, np.concatenate(([last], np.where(overlap > overlap_threshold)[0])))
        
        return [tuple(boxes[i]) for i in pick]
    
    def find_color(self, target_color: Tuple[int, int, int], region: Optional[Tuple[int, int, int, int]] = None,
                   tolerance: int = 10, max_results: int = 1) -> List[Tuple[int, int]]:
        """
        在屏幕或指定区域查找颜色
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差
            max_results: 最大返回结果数量
            
        Returns:
            找到的坐标列表 [(x, y), ...]
        """
        # 截取搜索区域
        img = self.capture_screen(region)
        
        # 创建颜色掩码
        lower_bound = np.array([max(0, c - tolerance) for c in target_color])
        upper_bound = np.array([min(255, c + tolerance) for c in target_color])
        
        mask = cv2.inRange(img, lower_bound, upper_bound)
        
        # 查找匹配的像素点
        y_coords, x_coords = np.where(mask == 255)
        
        # 转换为坐标列表
        matches = []
        for x, y in zip(x_coords, y_coords):
            # 如果指定了搜索区域，需要转换为屏幕坐标
            if region is not None:
                x += region[0]
                y += region[1]
            matches.append((int(x), int(y)))
        
        # 限制返回结果数量
        return matches[:max_results]
    
    def find_color_center(self, target_color: Tuple[int, int, int], 
                         region: Optional[Tuple[int, int, int, int]] = None,
                         tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        查找颜色并返回第一个匹配点的坐标
        
        Args:
            target_color: 目标颜色 (B, G, R)
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差
            
        Returns:
            第一个匹配点的坐标 (x, y)，未找到返回 None
        """
        results = self.find_color(target_color, region, tolerance, max_results=1)
        return results[0] if results else None

    def find_multi_color(self, color_points: List[Tuple[int, int, Tuple[int, int, int]]],
                        region: Optional[Tuple[int, int, int, int]] = None,
                        tolerance: int = 10) -> Optional[Tuple[int, int]]:
        """
        多点找色

        Args:
            color_points: 颜色点列表 [(相对 x, 相对 y, (B, G, R)), ...]
                         第一个点为基准点，其他点为相对于基准点的偏移
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            tolerance: 颜色容差

        Returns:
            基准点的坐标 (x, y)，未找到返回 None
        """
        if not color_points:
            return None

        # 第一个点作为基准点
        base_offset_x, base_offset_y, base_color = color_points[0]

        # 查找基准点的所有可能位置
        base_matches = self.find_color(base_color, region, tolerance, max_results=1000)

        # 检查每个基准点位置
        for base_x, base_y in base_matches:
            all_match = True

            # 检查其他所有点
            for offset_x, offset_y, target_color in color_points[1:]:
                check_x = base_x + offset_x - base_offset_x
                check_y = base_y + offset_y - base_offset_y

                # 检查坐标是否在有效范围内
                if (check_x < 0 or check_y < 0 or
                    check_x >= self.screen_width or check_y >= self.screen_height):
                    all_match = False
                    break

                # 获取该位置的颜色
                actual_color = self.get_pixel_color(check_x, check_y)

                # 检查颜色是否匹配
                if self.color_distance(actual_color, target_color) > tolerance * math.sqrt(3):
                    all_match = False
                    break

            if all_match:
                return (base_x, base_y)

        return None

    # ==================== 鼠标键盘操作 ====================

    def click(self, x: int, y: int, button: str = 'left', clicks: int = 1, interval: float = 0.1):
        """
        鼠标点击

        Args:
            x: X 坐标
            y: Y 坐标
            button: 鼠标按键 ('left', 'right', 'middle')
            clicks: 点击次数
            interval: 点击间隔
        """
        pyautogui.click(x, y, clicks=clicks, interval=interval, button=button)

    def double_click(self, x: int, y: int):
        """双击"""
        pyautogui.doubleClick(x, y)

    def right_click(self, x: int, y: int):
        """右键点击"""
        pyautogui.rightClick(x, y)

    def drag(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: float = 1.0):
        """
        拖拽操作

        Args:
            start_x: 起始 X 坐标
            start_y: 起始 Y 坐标
            end_x: 结束 X 坐标
            end_y: 结束 Y 坐标
            duration: 拖拽持续时间
        """
        pyautogui.drag(end_x - start_x, end_y - start_y, duration=duration)

    def scroll(self, x: int, y: int, clicks: int):
        """
        滚轮操作

        Args:
            x: X 坐标
            y: Y 坐标
            clicks: 滚动次数，正数向上，负数向下
        """
        pyautogui.scroll(clicks, x=x, y=y)

    def type_text(self, text: str, interval: float = 0.1):
        """
        输入文本

        Args:
            text: 要输入的文本
            interval: 字符间隔
        """
        pyautogui.typewrite(text, interval=interval)

    def press_key(self, key: str):
        """
        按键

        Args:
            key: 按键名称 (如 'enter', 'space', 'ctrl', 'alt' 等)
        """
        pyautogui.press(key)

    def key_combination(self, *keys):
        """
        组合键

        Args:
            keys: 按键组合 (如 'ctrl', 'c')
        """
        pyautogui.hotkey(*keys)

    # ==================== 图像处理和比较 ====================

    def compare_images(self, img1_path: str, img2_path: str, method: str = 'histogram') -> float:
        """
        比较两张图片的相似度

        Args:
            img1_path: 图片 1 路径
            img2_path: 图片 2 路径
            method: 比较方法 ('histogram', 'mse', 'ssim', 'template')

        Returns:
            相似度 (0-1)，1 表示完全相同
        """
        img1 = cv2.imread(img1_path)
        img2 = cv2.imread(img2_path)

        if img1 is None or img2 is None:
            return 0.0

        if method == 'histogram':
            return self._compare_histogram(img1, img2)
        elif method == 'mse':
            return self._compare_mse(img1, img2)
        elif method == 'ssim':
            return self._compare_ssim(img1, img2)
        elif method == 'template':
            return self._compare_template(img1, img2)
        else:
            return self._compare_histogram(img1, img2)

    def _compare_histogram(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用直方图比较图像相似度"""
        # 转换为 HSV 颜色空间
        hsv1 = cv2.cvtColor(img1, cv2.COLOR_BGR2HSV)
        hsv2 = cv2.cvtColor(img2, cv2.COLOR_BGR2HSV)

        # 计算直方图
        hist1 = cv2.calcHist([hsv1], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])
        hist2 = cv2.calcHist([hsv2], [0, 1, 2], None, [50, 60, 60], [0, 180, 0, 256, 0, 256])

        # 使用相关性比较
        correlation = cv2.compareHist(hist1, hist2, cv2.HISTCMP_CORREL)
        return max(0.0, correlation)  # 确保返回值在 0-1 之间

    def _compare_mse(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用均方误差比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 计算均方误差
        mse = np.mean((img1.astype(float) - img2.astype(float)) ** 2)

        # 转换为相似度 (MSE 越小，相似度越高)
        max_mse = 255.0 ** 2  # 最大可能的 MSE
        similarity = 1.0 - (mse / max_mse)
        return max(0.0, similarity)

    def _compare_ssim(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用结构相似性指数比较图像"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 计算 SSIM (简化版本)
        mu1 = cv2.GaussianBlur(gray1.astype(float), (11, 11), 1.5)
        mu2 = cv2.GaussianBlur(gray2.astype(float), (11, 11), 1.5)

        mu1_sq = mu1 * mu1
        mu2_sq = mu2 * mu2
        mu1_mu2 = mu1 * mu2

        sigma1_sq = cv2.GaussianBlur(gray1.astype(float) * gray1.astype(float), (11, 11), 1.5) - mu1_sq
        sigma2_sq = cv2.GaussianBlur(gray2.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu2_sq
        sigma12 = cv2.GaussianBlur(gray1.astype(float) * gray2.astype(float), (11, 11), 1.5) - mu1_mu2

        c1 = (0.01 * 255) ** 2
        c2 = (0.03 * 255) ** 2

        ssim_map = ((2 * mu1_mu2 + c1) * (2 * sigma12 + c2)) / ((mu1_sq + mu2_sq + c1) * (sigma1_sq + sigma2_sq + c2))
        return float(np.mean(ssim_map))

    def _compare_template(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """使用模板匹配比较图像相似度"""
        # 调整图片大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 转换为灰度图
        gray1 = cv2.cvtColor(img1, cv2.COLOR_BGR2GRAY)
        gray2 = cv2.cvtColor(img2, cv2.COLOR_BGR2GRAY)

        # 使用模板匹配
        result = cv2.matchTemplate(gray1, gray2, cv2.TM_CCOEFF_NORMED)
        return max(0.0, float(np.max(result)))  # 确保返回值非负

    def find_image_in_image(self, large_img_path: str, small_img_path: str, threshold: float = 0.8) -> Optional[Tuple[int, int]]:
        """
        在大图中查找小图的位置（专门用于区域图在全屏图中的定位）

        Args:
            large_img_path: 大图路径
            small_img_path: 小图路径
            threshold: 相似度阈值

        Returns:
            小图在大图中的位置 (x, y)，未找到返回 None
        """
        large_img = cv2.imread(large_img_path)
        small_img = cv2.imread(small_img_path)

        if large_img is None or small_img is None:
            return None

        # 转换为灰度图
        large_gray = cv2.cvtColor(large_img, cv2.COLOR_BGR2GRAY)
        small_gray = cv2.cvtColor(small_img, cv2.COLOR_BGR2GRAY)

        # 模板匹配
        result = cv2.matchTemplate(large_gray, small_gray, cv2.TM_CCOEFF_NORMED)

        # 查找最佳匹配位置
        _, max_val, _, max_loc = cv2.minMaxLoc(result)

        if max_val >= threshold:
            return max_loc
        else:
            return None

    def wait_for_image(self, template_path: str, timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      threshold: float = 0.8, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待图像出现

        Args:
            template_path: 模板图像路径
            timeout: 超时时间（秒）
            region: 搜索区域
            threshold: 相似度阈值
            check_interval: 检查间隔

        Returns:
            图像中心坐标，超时返回 None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_image_center(template_path, region, threshold)
            if result:
                return result
            time.sleep(check_interval)

        return None

    def wait_for_color(self, target_color: Tuple[int, int, int], timeout: float = 10.0,
                      region: Optional[Tuple[int, int, int, int]] = None,
                      tolerance: int = 10, check_interval: float = 0.5) -> Optional[Tuple[int, int]]:
        """
        等待颜色出现

        Args:
            target_color: 目标颜色
            timeout: 超时时间（秒）
            region: 搜索区域
            tolerance: 颜色容差
            check_interval: 检查间隔

        Returns:
            颜色坐标，超时返回 None
        """
        start_time = time.time()

        while time.time() - start_time < timeout:
            result = self.find_color_center(target_color, region, tolerance)
            if result:
                return result
            time.sleep(check_interval)

        return None

    # ==================== 字库和文字识别功能 ====================

    def load_font_library(self, library_path: str, library_name: str = "default") -> bool:
        """
        加载字库文件

        Args:
            library_path: 字库文件路径
            library_name: 字库名称

        Returns:
            是否加载成功
        """
        return self.font_manager.load_library(library_path, library_name)

    def find_character(self, char: str, region: Optional[Tuple[int, int, int, int]] = None,
                      threshold: float = 0.8, max_results: int = 1,
                      library_name: str = None) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找字符

        Args:
            char: 要查找的字符
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            max_results: 最大返回结果数量
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            找到的位置列表 [(x, y, width, height), ...]
        """
        # 获取字符模板
        templates = self.font_manager.get_character_templates(char, library_name)
        if not templates:
            print(f"字符 '{char}' 在字库中不存在")
            return []

        # 截取搜索区域
        screen_img = self.capture_screen(region)

        all_matches = []

        # 对每个模板进行匹配
        for template_data in templates:
            try:
                # 解码模板图像
                img_data = base64.b64decode(template_data["image"])
                img_array = np.frombuffer(img_data, np.uint8)
                template = cv2.imdecode(img_array, cv2.IMREAD_GRAYSCALE)

                if template is None:
                    continue

                # 转换屏幕图像为灰度
                screen_gray = cv2.cvtColor(screen_img, cv2.COLOR_BGR2GRAY)

                # 执行模板匹配
                result = cv2.matchTemplate(screen_gray, template, cv2.TM_CCOEFF_NORMED)

                # 查找匹配位置
                locations = np.where(result >= threshold)
                h, w = template.shape[:2]

                # 添加匹配结果
                for pt in zip(*locations[::-1]):
                    x, y = pt
                    # 如果指定了搜索区域，需要转换为屏幕坐标
                    if region is not None:
                        x += region[0]
                        y += region[1]

                    # 添加相似度信息
                    similarity = result[pt[1], pt[0]]
                    all_matches.append((x, y, w, h, similarity))

            except Exception as e:
                print(f"处理模板时出错：{e}")
                continue

        # 按相似度排序并去重
        if all_matches:
            # 按相似度降序排序
            all_matches.sort(key=lambda x: x[4], reverse=True)

            # 非极大值抑制去重
            boxes = [(x, y, w, h) for x, y, w, h, _ in all_matches]
            unique_boxes = self._non_max_suppression(boxes)

            return unique_boxes[:max_results]

        return []

    def find_character_center(self, char: str, region: Optional[Tuple[int, int, int, int]] = None,
                             threshold: float = 0.8, library_name: str = None) -> Optional[Tuple[int, int]]:
        """
        查找字符并返回中心坐标

        Args:
            char: 要查找的字符
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            字符中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_character(char, region, threshold, max_results=1, library_name=library_name)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None

    def find_text(self, text: str, region: Optional[Tuple[int, int, int, int]] = None,
                  threshold: float = 0.8, char_spacing: int = 5,
                  library_name: str = None) -> List[Tuple[int, int, int, int]]:
        """
        在屏幕或指定区域查找文字串

        Args:
            text: 要查找的文字串
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            char_spacing: 字符间最大间距
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            找到的文字串位置列表 [(x, y, width, height), ...]
        """
        if not text:
            return []

        # 查找第一个字符的所有位置
        first_char = text[0]
        first_char_positions = self.find_character(first_char, region, threshold, max_results=100, library_name=library_name)

        if not first_char_positions:
            return []

        text_matches = []

        # 对每个第一个字符的位置，尝试匹配完整文字串
        for first_x, first_y, first_w, first_h in first_char_positions:
            match_success = True
            current_x = first_x + first_w
            text_width = first_w
            text_height = first_h

            # 逐个匹配后续字符
            for i, char in enumerate(text[1:], 1):
                # 在当前位置附近查找字符
                search_region = (current_x, first_y - char_spacing,
                               char_spacing * 3, first_h + char_spacing * 2)

                char_positions = self.find_character(char, search_region, threshold, max_results=1, library_name=library_name)

                if not char_positions:
                    match_success = False
                    break

                char_x, char_y, char_w, char_h = char_positions[0]

                # 检查字符位置是否合理（垂直对齐，水平间距合理）
                if (abs(char_y - first_y) > char_spacing or
                    char_x - current_x > char_spacing):
                    match_success = False
                    break

                # 更新文字串的边界
                text_width = char_x + char_w - first_x
                text_height = max(text_height, char_h)
                current_x = char_x + char_w

            if match_success:
                text_matches.append((first_x, first_y, text_width, text_height))

        return text_matches

    def find_text_center(self, text: str, region: Optional[Tuple[int, int, int, int]] = None,
                        threshold: float = 0.8, char_spacing: int = 5,
                        library_name: str = None) -> Optional[Tuple[int, int]]:
        """
        查找文字串并返回中心坐标

        Args:
            text: 要查找的文字串
            region: 搜索区域 (x, y, width, height)，None 表示全屏
            threshold: 相似度阈值 (0-1)
            char_spacing: 字符间最大间距
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            文字串中心坐标 (x, y)，未找到返回 None
        """
        results = self.find_text(text, region, threshold, char_spacing, library_name)
        if results:
            x, y, w, h = results[0]
            return (x + w // 2, y + h // 2)
        return None


# ==================== 字库制作功能 ====================

class FontLibraryCreator:
    """
    字库制作类
    用于从图像中提取字符，创建字库文件
    """

    def __init__(self):
        """初始化字库制作器"""
        self.library_data = {
            "version": "1.0",
            "characters": {},
            "metadata": {
                "created_time": datetime.now().isoformat(),
                "total_chars": 0,
                "description": ""
            }
        }

    def extract_characters_from_image(self, image_path: str, text_content: str,
                                    auto_segment: bool = True,
                                    char_spacing: int = 2) -> Dict[str, List[np.ndarray]]:
        """
        从图像中提取字符

        Args:
            image_path: 包含文字的图像路径
            text_content: 图像中的文字内容（用于标注）
            auto_segment: 是否自动分割字符
            char_spacing: 字符间距（用于分割）

        Returns:
            字符字典 {字符: [图像数组列表]}
        """
        # 读取图像
        img = cv2.imread(image_path)
        if img is None:
            raise FileNotFoundError(f"无法读取图像文件：{image_path}")

        # 转换为灰度图
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        if auto_segment:
            # 自动分割字符
            char_images = self._auto_segment_characters(gray, char_spacing)
        else:
            # 手动分割（返回整个图像，需要用户手动处理）
            char_images = [gray]

        # 将分割的字符与文字内容对应
        char_dict = {}
        if len(char_images) == len(text_content):
            for i, char in enumerate(text_content):
                if char not in char_dict:
                    char_dict[char] = []
                char_dict[char].append(char_images[i])
        else:
            # 如果数量不匹配，返回原图像供手动处理
            print(f"警告：检测到 {len(char_images)} 个字符，但文本有 {len(text_content)} 个字符")
            char_dict["_unmatched_"] = char_images

        return char_dict

    def _auto_segment_characters(self, gray_img: np.ndarray, char_spacing: int = 2) -> List[np.ndarray]:
        """
        自动分割字符

        Args:
            gray_img: 灰度图像
            char_spacing: 字符间距

        Returns:
            分割后的字符图像列表
        """
        # 二值化
        _, binary = cv2.threshold(gray_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 获取边界框并排序
        bounding_boxes = []
        for contour in contours:
            x, y, w, h = cv2.boundingRect(contour)
            # 过滤太小的区域
            if w > 5 and h > 5:
                bounding_boxes.append((x, y, w, h))

        # 按 x 坐标排序
        bounding_boxes.sort(key=lambda box: box[0])

        # 提取字符图像
        char_images = []
        for x, y, w, h in bounding_boxes:
            char_img = gray_img[y:y+h, x:x+w]
            char_images.append(char_img)

        return char_images

    def extract_features(self, char_img: np.ndarray) -> Dict[str, Any]:
        """
        提取字符特征

        Args:
            char_img: 字符图像

        Returns:
            特征字典
        """
        features = {}

        # 基本尺寸特征
        features['width'] = char_img.shape[1]
        features['height'] = char_img.shape[0]
        features['aspect_ratio'] = features['width'] / features['height']

        # 二值化
        _, binary = cv2.threshold(char_img, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 像素密度特征
        total_pixels = char_img.shape[0] * char_img.shape[1]
        white_pixels = np.sum(binary == 255)
        features['density'] = white_pixels / total_pixels

        # 轮廓特征
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)
        if contours:
            largest_contour = max(contours, key=cv2.contourArea)
            features['contour_area'] = cv2.contourArea(largest_contour)
            features['contour_perimeter'] = cv2.arcLength(largest_contour, True)
        else:
            features['contour_area'] = 0
            features['contour_perimeter'] = 0

        # 投影特征（水平和垂直投影）
        h_projection = np.sum(binary, axis=0)
        v_projection = np.sum(binary, axis=1)
        features['h_projection'] = h_projection.tolist()
        features['v_projection'] = v_projection.tolist()

        return features

    def add_character_to_library(self, char: str, char_img: np.ndarray,
                                font_info: Optional[Dict[str, Any]] = None):
        """
        添加字符到字库

        Args:
            char: 字符
            char_img: 字符图像
            font_info: 字体信息
        """
        if char not in self.library_data["characters"]:
            self.library_data["characters"][char] = {"templates": []}

        # 提取特征
        features = self.extract_features(char_img)

        # 将图像转换为 base64
        _, buffer = cv2.imencode('.png', char_img)
        img_base64 = base64.b64encode(buffer).decode('utf-8')

        # 创建模板数据
        template_data = {
            "image": img_base64,
            "width": char_img.shape[1],
            "height": char_img.shape[0],
            "features": features,
            "font_info": font_info or {}
        }

        self.library_data["characters"][char]["templates"].append(template_data)
        self.library_data["metadata"]["total_chars"] = len(self.library_data["characters"])

    def create_from_image(self, image_path: str, text_content: str,
                         font_info: Optional[Dict[str, Any]] = None,
                         auto_segment: bool = True) -> Dict[str, Any]:
        """
        从单个图像创建字库

        Args:
            image_path: 图像路径
            text_content: 图像中的文字内容
            font_info: 字体信息
            auto_segment: 是否自动分割

        Returns:
            字库数据
        """
        char_dict = self.extract_characters_from_image(image_path, text_content, auto_segment)

        for char, char_images in char_dict.items():
            for char_img in char_images:
                self.add_character_to_library(char, char_img, font_info)

        return self.library_data

    def create_from_multiple_images(self, image_text_pairs: List[Tuple[str, str]],
                                   font_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """
        从多个图像创建字库

        Args:
            image_text_pairs: [(图像路径, 文字内容), ...]
            font_info: 字体信息

        Returns:
            字库数据
        """
        for image_path, text_content in image_text_pairs:
            try:
                self.create_from_image(image_path, text_content, font_info, auto_segment=True)
            except Exception as e:
                print(f"处理图像 {image_path} 时出错：{e}")

        return self.library_data

    def save_library(self, output_path: str, description: str = "") -> bool:
        """
        保存字库到文件

        Args:
            output_path: 输出文件路径
            description: 字库描述

        Returns:
            是否保存成功
        """
        try:
            self.library_data["metadata"]["description"] = description
            self.library_data["metadata"]["saved_time"] = datetime.now().isoformat()

            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.library_data, f, ensure_ascii=False, indent=2)

            print(f"字库已保存到：{output_path}")
            print(f"包含 {self.library_data['metadata']['total_chars']} 个字符")
            return True

        except Exception as e:
            print(f"保存字库失败：{e}")
            return False

    def preview_segmentation(self, image_path: str, output_path: str = None) -> List[Tuple[int, int, int, int]]:
        """
        预览字符分割结果

        Args:
            image_path: 图像路径
            output_path: 预览图像保存路径

        Returns:
            边界框列表 [(x, y, w, h), ...]
        """
        img = cv2.imread(image_path)
        gray = cv2.cvtColor(img, cv2.COLOR_BGR2GRAY)

        # 二值化
        _, binary = cv2.threshold(gray, 0, 255, cv2.THRESH_BINARY_INV + cv2.THRESH_OTSU)

        # 查找轮廓
        contours, _ = cv2.findContours(binary, cv2.RETR_EXTERNAL, cv2.CHAIN_APPROX_SIMPLE)

        # 获取边界框
        bounding_boxes = []
        preview_img = img.copy()

        for i, contour in enumerate(contours):
            x, y, w, h = cv2.boundingRect(contour)
            if w > 5 and h > 5:  # 过滤太小的区域
                bounding_boxes.append((x, y, w, h))
                # 在预览图上绘制边界框
                cv2.rectangle(preview_img, (x, y), (x + w, y + h), (0, 255, 0), 2)
                cv2.putText(preview_img, str(i), (x, y - 5), cv2.FONT_HERSHEY_SIMPLEX, 0.5, (0, 255, 0), 1)

        # 保存预览图
        if output_path:
            cv2.imwrite(output_path, preview_img)
            print(f"分割预览图已保存到：{output_path}")

        return bounding_boxes


# ==================== 字库管理功能 ====================

class FontLibraryManager:
    """
    字库管理类
    用于加载、保存、合并和管理字库
    """

    def __init__(self):
        """初始化字库管理器"""
        self.libraries = {}  # 存储多个字库
        self.current_library = None

    def load_library(self, library_path: str, library_name: str = "default") -> bool:
        """
        加载字库文件

        Args:
            library_path: 字库文件路径
            library_name: 字库名称

        Returns:
            是否加载成功
        """
        try:
            with open(library_path, 'r', encoding='utf-8') as f:
                library_data = json.load(f)

            self.libraries[library_name] = library_data
            self.current_library = library_name

            char_count = library_data.get("metadata", {}).get("total_chars", 0)
            print(f"字库 '{library_name}' 加载成功，包含 {char_count} 个字符")
            return True

        except Exception as e:
            print(f"加载字库失败：{e}")
            return False

    def save_library(self, library_name: str, output_path: str) -> bool:
        """
        保存指定字库到文件

        Args:
            library_name: 字库名称
            output_path: 输出路径

        Returns:
            是否保存成功
        """
        if library_name not in self.libraries:
            print(f"字库 '{library_name}' 不存在")
            return False

        try:
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(self.libraries[library_name], f, ensure_ascii=False, indent=2)

            print(f"字库 '{library_name}' 已保存到：{output_path}")
            return True

        except Exception as e:
            print(f"保存字库失败：{e}")
            return False

    def merge_libraries(self, library_names: List[str], merged_name: str = "merged") -> bool:
        """
        合并多个字库

        Args:
            library_names: 要合并的字库名称列表
            merged_name: 合并后的字库名称

        Returns:
            是否合并成功
        """
        if not library_names:
            return False

        # 创建新的合并字库
        merged_library = {
            "version": "1.0",
            "characters": {},
            "metadata": {
                "created_time": datetime.now().isoformat(),
                "total_chars": 0,
                "description": f"合并自: {', '.join(library_names)}"
            }
        }

        # 合并字符数据
        for lib_name in library_names:
            if lib_name not in self.libraries:
                print(f"警告：字库 '{lib_name}' 不存在，跳过")
                continue

            lib_data = self.libraries[lib_name]
            for char, char_data in lib_data.get("characters", {}).items():
                if char not in merged_library["characters"]:
                    merged_library["characters"][char] = {"templates": []}

                # 合并模板
                merged_library["characters"][char]["templates"].extend(
                    char_data.get("templates", [])
                )

        merged_library["metadata"]["total_chars"] = len(merged_library["characters"])
        self.libraries[merged_name] = merged_library

        print(f"已合并 {len(library_names)} 个字库，新字库包含 {merged_library['metadata']['total_chars']} 个字符")
        return True

    def get_character_templates(self, char: str, library_name: str = None) -> List[Dict[str, Any]]:
        """
        获取字符的所有模板

        Args:
            char: 字符
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            模板列表
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return []

        char_data = self.libraries[lib_name].get("characters", {}).get(char, {})
        return char_data.get("templates", [])

    def list_characters(self, library_name: str = None) -> List[str]:
        """
        列出字库中的所有字符

        Args:
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            字符列表
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return []

        return list(self.libraries[lib_name].get("characters", {}).keys())

    def get_library_info(self, library_name: str = None) -> Dict[str, Any]:
        """
        获取字库信息

        Args:
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            字库信息
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return {}

        return self.libraries[lib_name].get("metadata", {})

    def optimize_library(self, library_name: str = None,
                        remove_duplicates: bool = True,
                        similarity_threshold: float = 0.95) -> bool:
        """
        优化字库（去重、压缩等）

        Args:
            library_name: 字库名称，None 表示使用当前字库
            remove_duplicates: 是否移除重复模板
            similarity_threshold: 相似度阈值，超过此值认为是重复

        Returns:
            是否优化成功
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            print(f"字库 '{lib_name}' 不存在")
            return False

        library = self.libraries[lib_name]
        original_count = 0
        optimized_count = 0

        # 统计原始模板数量
        for char_data in library.get("characters", {}).values():
            original_count += len(char_data.get("templates", []))

        if remove_duplicates:
            # 对每个字符的模板进行去重
            for char, char_data in library.get("characters", {}).items():
                templates = char_data.get("templates", [])
                if len(templates) <= 1:
                    continue

                # 去重处理
                unique_templates = self._remove_duplicate_templates(templates, similarity_threshold)
                char_data["templates"] = unique_templates

                print(f"字符 '{char}': {len(templates)} -> {len(unique_templates)} 个模板")

        # 统计优化后模板数量
        for char_data in library.get("characters", {}).values():
            optimized_count += len(char_data.get("templates", []))

        # 更新元数据
        library["metadata"]["optimized_time"] = datetime.now().isoformat()
        library["metadata"]["optimization_info"] = {
            "original_templates": original_count,
            "optimized_templates": optimized_count,
            "reduction_rate": (original_count - optimized_count) / original_count if original_count > 0 else 0
        }

        print(f"字库优化完成：{original_count} -> {optimized_count} 个模板")
        return True

    def _remove_duplicate_templates(self, templates: List[Dict[str, Any]],
                                   threshold: float = 0.95) -> List[Dict[str, Any]]:
        """
        移除重复的模板

        Args:
            templates: 模板列表
            threshold: 相似度阈值

        Returns:
            去重后的模板列表
        """
        if len(templates) <= 1:
            return templates

        unique_templates = []

        for template in templates:
            is_duplicate = False

            # 解码当前模板图像
            try:
                img_data = base64.b64decode(template["image"])
                img_array = np.frombuffer(img_data, np.uint8)
                current_img = cv2.imdecode(img_array, cv2.IMREAD_GRAYSCALE)

                if current_img is None:
                    continue

                # 与已有的唯一模板比较
                for unique_template in unique_templates:
                    unique_img_data = base64.b64decode(unique_template["image"])
                    unique_img_array = np.frombuffer(unique_img_data, np.uint8)
                    unique_img = cv2.imdecode(unique_img_array, cv2.IMREAD_GRAYSCALE)

                    if unique_img is None:
                        continue

                    # 计算相似度
                    similarity = self._calculate_template_similarity(current_img, unique_img)

                    if similarity >= threshold:
                        is_duplicate = True
                        break

                if not is_duplicate:
                    unique_templates.append(template)

            except Exception as e:
                print(f"处理模板时出错：{e}")
                continue

        return unique_templates

    def _calculate_template_similarity(self, img1: np.ndarray, img2: np.ndarray) -> float:
        """
        计算两个模板图像的相似度

        Args:
            img1: 图像1
            img2: 图像2

        Returns:
            相似度 (0-1)
        """
        # 调整图像大小一致
        if img1.shape != img2.shape:
            img2 = cv2.resize(img2, (img1.shape[1], img1.shape[0]))

        # 使用模板匹配计算相似度
        result = cv2.matchTemplate(img1, img2, cv2.TM_CCOEFF_NORMED)
        return float(np.max(result))

    def analyze_library(self, library_name: str = None) -> Dict[str, Any]:
        """
        分析字库统计信息

        Args:
            library_name: 字库名称，None 表示使用当前字库

        Returns:
            分析结果
        """
        lib_name = library_name or self.current_library
        if lib_name not in self.libraries:
            return {}

        library = self.libraries[lib_name]
        characters = library.get("characters", {})

        analysis = {
            "total_characters": len(characters),
            "total_templates": 0,
            "character_stats": {},
            "template_size_stats": {
                "min_width": float('inf'),
                "max_width": 0,
                "min_height": float('inf'),
                "max_height": 0,
                "avg_width": 0,
                "avg_height": 0
            }
        }

        total_width = 0
        total_height = 0

        # 分析每个字符
        for char, char_data in characters.items():
            templates = char_data.get("templates", [])
            template_count = len(templates)
            analysis["total_templates"] += template_count

            analysis["character_stats"][char] = {
                "template_count": template_count,
                "sizes": []
            }

            # 分析模板尺寸
            for template in templates:
                width = template.get("width", 0)
                height = template.get("height", 0)

                analysis["character_stats"][char]["sizes"].append((width, height))

                # 更新统计信息
                analysis["template_size_stats"]["min_width"] = min(analysis["template_size_stats"]["min_width"], width)
                analysis["template_size_stats"]["max_width"] = max(analysis["template_size_stats"]["max_width"], width)
                analysis["template_size_stats"]["min_height"] = min(analysis["template_size_stats"]["min_height"], height)
                analysis["template_size_stats"]["max_height"] = max(analysis["template_size_stats"]["max_height"], height)

                total_width += width
                total_height += height

        # 计算平均尺寸
        if analysis["total_templates"] > 0:
            analysis["template_size_stats"]["avg_width"] = total_width / analysis["total_templates"]
            analysis["template_size_stats"]["avg_height"] = total_height / analysis["total_templates"]

        # 处理无限值
        if analysis["template_size_stats"]["min_width"] == float('inf'):
            analysis["template_size_stats"]["min_width"] = 0
        if analysis["template_size_stats"]["min_height"] == float('inf'):
            analysis["template_size_stats"]["min_height"] = 0

        return analysis


# ==================== 使用示例和测试 ====================

def demo_basic_functions():
    """基础功能演示"""
    print("=== 图色脚本功能演示 ===")

    # 创建脚本实例
    script = ImageColorScript()

    print("1. 截图功能演示")
    # 全屏截图
    if script.save_screenshot("full_screen.png"):
        print("   ✓ 全屏截图保存成功：full_screen.png")

    # 区域截图 (左上角 500x300 区域)
    if script.save_screenshot("region_screen.png", region=(0, 0, 500, 300)):
        print("   ✓ 区域截图保存成功：region_screen.png")

    print("\n2. 颜色获取演示")
    # 获取屏幕中心点颜色
    center_x, center_y = script.screen_width // 2, script.screen_height // 2
    color = script.get_pixel_color(center_x, center_y)
    print(f"   屏幕中心点 ({center_x}, {center_y}) 的颜色：{color}")

    print("\n3. 找色功能演示")
    # 查找白色 (255, 255, 255)
    white_positions = script.find_color((255, 255, 255), max_results=5)
    print(f"   找到 {len(white_positions)} 个白色像素点")
    if white_positions:
        print(f"   前几个位置：{white_positions[:3]}")


def demo_advanced_functions():
    """高级功能演示"""
    print("\n=== 高级功能演示 ===")

    script = ImageColorScript()

    print("1. 多点找色演示")
    # 定义一个简单的颜色模式 (例如：查找一个小的白色方块)
    color_pattern = [
        (0, 0, (255, 255, 255)),    # 基准点：白色
        (1, 0, (255, 255, 255)),    # 右边一个像素：白色
        (0, 1, (255, 255, 255)),    # 下边一个像素：白色
        (1, 1, (255, 255, 255)),    # 右下角：白色
    ]

    result = script.find_multi_color(color_pattern, tolerance=10)
    if result:
        print(f"   ✓ 找到颜色模式，位置：{result}")
    else:
        print("   ✗ 未找到指定的颜色模式")

    print("\n2. 等待功能演示")
    print("   注意：等待功能需要实际的图像文件，这里仅演示调用方式")

    print("\n3. 鼠标键盘操作演示")
    print("   注意：以下操作会实际控制鼠标键盘，请小心使用")

    # 获取当前鼠标位置
    current_pos = pyautogui.position()
    print(f"   当前鼠标位置：{current_pos}")


def test_color_functions():
    """测试颜色相关功能"""
    print("\n=== 颜色功能测试 ===")

    script = ImageColorScript()

    # 测试颜色距离计算
    color1 = (255, 0, 0)    # 红色
    color2 = (0, 255, 0)    # 绿色
    color3 = (255, 10, 10)  # 接近红色

    distance1 = script.color_distance(color1, color2)
    distance2 = script.color_distance(color1, color3)

    print(f"红色与绿色的距离：{distance1:.2f}")
    print(f"红色与接近红色的距离：{distance2:.2f}")

    # 测试在小区域内查找颜色
    region = (0, 0, 100, 100)  # 左上角 100x100 区域
    colors_found = script.find_color((255, 255, 255), region=region, max_results=10)
    print(f"在区域 {region} 内找到 {len(colors_found)} 个白色像素")


def demo_font_library_creation():
    """字库制作功能演示"""
    print("\n=== 字库制作功能演示 ===")

    try:
        # 创建字库制作器
        creator = FontLibraryCreator()

        print("1. 字库制作演示")
        print("   注意：需要准备包含文字的图像文件进行测试")

        # 示例：从图像创建字库（需要实际的图像文件）
        # creator.create_from_image("sample_text.png", "示例文字")
        # creator.save_library("sample_font.json", "示例字库")

        print("   字库制作功能已就绪，可使用以下方法：")
        print("   • create_from_image() - 从单个图像创建字库")
        print("   • create_from_multiple_images() - 从多个图像创建字库")
        print("   • preview_segmentation() - 预览字符分割结果")
        print("   • save_library() - 保存字库文件")

    except Exception as e:
        print(f"字库制作演示出错：{e}")


def demo_font_library_management():
    """字库管理功能演示"""
    print("\n=== 字库管理功能演示 ===")

    try:
        # 创建字库管理器
        manager = FontLibraryManager()

        print("1. 字库管理演示")
        print("   字库管理功能已就绪，可使用以下方法：")
        print("   • load_library() - 加载字库文件")
        print("   • save_library() - 保存字库文件")
        print("   • merge_libraries() - 合并多个字库")
        print("   • optimize_library() - 优化字库（去重等）")
        print("   • analyze_library() - 分析字库统计信息")
        print("   • list_characters() - 列出字库中的字符")

        # 示例：分析字库（如果有字库文件的话）
        # manager.load_library("sample_font.json")
        # analysis = manager.analyze_library()
        # print(f"   字库分析结果：{analysis}")

    except Exception as e:
        print(f"字库管理演示出错：{e}")


def demo_text_recognition():
    """文字识别功能演示"""
    print("\n=== 文字识别功能演示 ===")

    try:
        # 创建图色脚本实例
        script = ImageColorScript()

        print("1. 文字识别演示")
        print("   注意：需要先加载字库文件才能进行文字识别")

        # 示例：加载字库并查找文字（需要实际的字库文件）
        # script.load_font_library("sample_font.json")
        # positions = script.find_character("字")
        # text_positions = script.find_text("示例文字")

        print("   文字识别功能已就绪，可使用以下方法：")
        print("   • load_font_library() - 加载字库文件")
        print("   • find_character() - 查找单个字符")
        print("   • find_character_center() - 查找字符中心坐标")
        print("   • find_text() - 查找文字串")
        print("   • find_text_center() - 查找文字串中心坐标")

    except Exception as e:
        print(f"文字识别演示出错：{e}")


def create_sample_font_library():
    """创建示例字库（用于测试）"""
    print("\n=== 创建示例字库 ===")

    try:
        # 创建一个简单的示例字库
        creator = FontLibraryCreator()

        # 创建一些简单的字符图像（用于演示）
        import numpy as np

        # 创建简单的数字字符图像
        for digit in "0123456789":
            # 创建一个简单的数字图像（实际应用中应该从真实图像提取）
            char_img = np.zeros((20, 15), dtype=np.uint8)

            # 简单绘制数字轮廓（这里只是示例，实际应该使用真实的字符图像）
            if digit == "0":
                cv2.rectangle(char_img, (2, 2), (12, 17), 255, 2)
            elif digit == "1":
                cv2.line(char_img, (7, 2), (7, 17), 255, 2)
            # ... 可以添加更多数字的绘制逻辑

            creator.add_character_to_library(digit, char_img, {"type": "digit", "size": "small"})

        # 保存示例字库
        if creator.save_library("sample_digits.json", "示例数字字库"):
            print("   ✓ 示例数字字库创建成功：sample_digits.json")
            return True

    except Exception as e:
        print(f"创建示例字库失败：{e}")
        return False


def test_complete_workflow():
    """完整工作流程测试"""
    print("\n=== 完整工作流程测试 ===")

    try:
        # 1. 创建示例字库
        print("1. 创建示例字库...")
        if not create_sample_font_library():
            print("   示例字库创建失败，跳过后续测试")
            return

        # 2. 测试字库管理
        print("\n2. 测试字库管理...")
        manager = FontLibraryManager()

        if manager.load_library("sample_digits.json", "digits"):
            print("   ✓ 字库加载成功")

            # 分析字库
            analysis = manager.analyze_library("digits")
            print(f"   字库包含 {analysis.get('total_characters', 0)} 个字符")
            print(f"   总共 {analysis.get('total_templates', 0)} 个模板")

            # 列出字符
            characters = manager.list_characters("digits")
            print(f"   字符列表：{characters}")

        # 3. 测试文字识别
        print("\n3. 测试文字识别...")
        script = ImageColorScript()

        if script.load_font_library("sample_digits.json", "digits"):
            print("   ✓ 字库加载到识别引擎成功")
            print("   现在可以使用 find_character() 和 find_text() 方法")

            # 注意：实际的文字识别需要屏幕上有对应的文字
            print("   提示：在屏幕上显示数字，然后调用以下方法进行识别：")
            print("   positions = script.find_character('1')")
            print("   text_pos = script.find_text('123')")

        print("\n✓ 完整工作流程测试完成")

    except Exception as e:
        print(f"完整工作流程测试失败：{e}")


def main():
    """主函数 - 运行所有演示"""
    try:
        # 原有功能演示
        demo_basic_functions()
        demo_advanced_functions()
        test_color_functions()

        # 新增字库功能演示
        demo_font_library_creation()
        demo_font_library_management()
        demo_text_recognition()
        test_complete_workflow()

        print("\n=== 演示完成 ===")
        print("所有功能演示已完成！")
        print("\n可用的主要功能：")
        print("• capture_screen() - 截图")
        print("• save_screenshot() - 保存截图")
        print("• find_image() - 找图")
        print("• find_color() - 找色")
        print("• find_multi_color() - 多点找色")
        print("• get_pixel_color() - 获取像素颜色")
        print("• click() - 鼠标点击")
        print("• type_text() - 输入文本")
        print("• wait_for_image() - 等待图像")
        print("• compare_images() - 图像比较")
        print("• find_image_in_image() - 在大图中查找小图")

        print("\n新增字库功能：")
        print("• FontLibraryCreator - 字库制作")
        print("• FontLibraryManager - 字库管理")
        print("• find_character() - 查找字符")
        print("• find_text() - 查找文字串")
        print("• load_font_library() - 加载字库")

    except Exception as e:
        print(f"演示过程中出现错误：{e}")
        print("请确保已安装所需的依赖库：")
        print("pip install opencv-python numpy mss pyautogui")


if __name__ == "__main__":
    main()
