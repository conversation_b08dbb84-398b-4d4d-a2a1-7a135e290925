<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="2cb0fbd4-8839-4023-8c23-3e6332ff7ea2" name="Changes" comment="# 功能优化&#10;- 点击编辑选中步骤按钮后，弹出来的窗口，因为高度不够，导致确定和取消按钮无法点击&#10;- 点击菜单中的设置的时候，弹出来的窗口不是在父窗体居中显示">
      <change beforePath="$PROJECT_DIR$/zhaoTuZhaoSe.py" beforeDir="false" afterPath="$PROJECT_DIR$/zhaoTuZhaoSe.py" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$" value="feature/ui-automation-workflow" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProjectId" id="2z5JSmCpD8IQ1uMCWBwzLwBwm3x" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "WebServerToolWindowFactoryState": "false",
    "last_opened_file_path": "D:/software/anaconda3",
    "nodejs_package_manager_path": "npm"
  }
}]]></component>
  <component name="RunManager" selected="Python.button_style_demo">
    <configuration name="automation_ui" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PythonTuSe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/automation_ui.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="button_style_demo" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PythonTuSe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/button_style_demo.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="improved_search_solution" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PythonTuSe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/improved_search_solution.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_import_only" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PythonTuSe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_import_only.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="test_jules_solution" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="PythonTuSe" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <EXTENSION ID="PythonCoverageRunConfigurationExtension" runner="coverage.py" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/test_jules_solution.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.button_style_demo" />
        <item itemvalue="Python.automation_ui" />
        <item itemvalue="Python.test_import_only" />
        <item itemvalue="Python.test_jules_solution" />
        <item itemvalue="Python.improved_search_solution" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="2cb0fbd4-8839-4023-8c23-3e6332ff7ea2" name="Changes" comment="" />
      <created>1751015007790</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1751015007790</updated>
      <workItem from="1751015012504" duration="2684000" />
      <workItem from="1751246607952" duration="3588000" />
      <workItem from="1751416194052" duration="12786000" />
    </task>
    <task id="LOCAL-00001" summary="更新模板">
      <created>1751017088258</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1751017088258</updated>
    </task>
    <task id="LOCAL-00002" summary="1.增加工作流数据保存&#10;2.增加屏幕可见部分滚动条&#10;3.调整布局样式">
      <created>1751263871447</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1751263871449</updated>
    </task>
    <task id="LOCAL-00003" summary="# UI优化针，对于automation_ui.py的UI界面，按钮样式可以参考button_style_demo.py文件&#10;- 脚本工作流frame中的按钮，是不是该横向排列，排列不下了，在换行重新排列&#10;- 按钮灰色的背景，配白色的字，有点看不清，需要调整&#10;- 允许你对添加新标签面板容器中的元素，进行重新排版&#10;- 脚本工作流改进（automation_ui.py）&#10;- 1. 工作流目前只有写死的执行间隔，无法自定义执行间隔时间，界面需要新增这个功能&#10;- 2. 工作流保存操作，保存的时候，没有保存添加的图片模板，导致重启程序后，导入之前已保存的工作流，无法正常工作&#10;- 3. 新增快捷键设置菜单：工作流的启动和停止，可以自己设定快捷键，默认快捷键是F0开始，F12停止 &#10;- 请你先分析这些需求，然后制定计划，最后根据计划任务实施修改">
      <created>1751422594491</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1751422594491</updated>
    </task>
    <task id="LOCAL-00004" summary="# UI优化针，对于automation_ui.py的UI界面，按钮样式可以参考button_style_demo.py文件&#10;- 脚本工作流frame中的按钮，是不是该横向排列，排列不下了，在换行重新排列&#10;- 按钮灰色的背景，配白色的字，有点看不清，需要调整&#10;- 允许你对添加新标签面板容器中的元素，进行重新排版&#10;- 脚本工作流改进（automation_ui.py）&#10;- 1. 工作流目前只有写死的执行间隔，无法自定义执行间隔时间，界面需要新增这个功能&#10;- 2. 工作流保存操作，保存的时候，没有保存添加的图片模板，导致重启程序后，导入之前已保存的工作流，无法正常工作&#10;- 3. 新增快捷键设置菜单：工作流的启动和停止，可以自己设定快捷键，默认快捷键是F0开始，F12停止 &#10;- 请你先分析这些需求，然后制定计划，最后根据计划任务实施修改">
      <created>1751424065499</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1751424065499</updated>
    </task>
    <task id="LOCAL-00005" summary="# UI优&#10;- 调整界面布局">
      <created>1751424961811</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1751424961811</updated>
    </task>
    <task id="LOCAL-00006" summary="# 功能优化&#10;1、增加工作流中的脚本，可以编辑等待时长（间隔多久执行下一步骤，单位秒）">
      <created>1751426147998</created>
      <option name="number" value="00006" />
      <option name="presentableId" value="LOCAL-00006" />
      <option name="project" value="LOCAL" />
      <updated>1751426147998</updated>
    </task>
    <task id="LOCAL-00007" summary="# 功能优化&#10;1、增加工作流中的脚本，新增可以编辑偏移">
      <created>1751434846121</created>
      <option name="number" value="00007" />
      <option name="presentableId" value="LOCAL-00007" />
      <option name="project" value="LOCAL" />
      <updated>1751434846121</updated>
    </task>
    <task id="LOCAL-00008" summary="# 功能优化&#10;- 点击编辑选中步骤按钮后，弹出来的窗口，因为高度不够，导致确定和取消按钮无法点击&#10;- 点击菜单中的设置的时候，弹出来的窗口不是在父窗体居中显示">
      <created>1751435321276</created>
      <option name="number" value="00008" />
      <option name="presentableId" value="LOCAL-00008" />
      <option name="project" value="LOCAL" />
      <updated>1751435321276</updated>
    </task>
    <option name="localTasksCounter" value="9" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <MESSAGE value="更新模板" />
    <MESSAGE value="1.增加工作流数据保存&#10;2.增加屏幕可见部分滚动条&#10;3.调整布局样式" />
    <MESSAGE value="# UI优化针，对于automation_ui.py的UI界面，按钮样式可以参考button_style_demo.py文件&#10;- 脚本工作流frame中的按钮，是不是该横向排列，排列不下了，在换行重新排列&#10;- 按钮灰色的背景，配白色的字，有点看不清，需要调整&#10;- 允许你对添加新标签面板容器中的元素，进行重新排版&#10;- 脚本工作流改进（automation_ui.py）&#10;- 1. 工作流目前只有写死的执行间隔，无法自定义执行间隔时间，界面需要新增这个功能&#10;- 2. 工作流保存操作，保存的时候，没有保存添加的图片模板，导致重启程序后，导入之前已保存的工作流，无法正常工作&#10;- 3. 新增快捷键设置菜单：工作流的启动和停止，可以自己设定快捷键，默认快捷键是F0开始，F12停止 &#10;- 请你先分析这些需求，然后制定计划，最后根据计划任务实施修改" />
    <MESSAGE value="# UI优&#10;- 调整界面布局" />
    <MESSAGE value="# 功能优化&#10;1、增加工作流中的脚本，可以编辑等待时长（间隔多久执行下一步骤，单位秒）" />
    <MESSAGE value="# 功能优化&#10;1、增加工作流中的脚本，新增可以编辑偏移" />
    <MESSAGE value="# 功能优化&#10;- 点击编辑选中步骤按钮后，弹出来的窗口，因为高度不够，导致确定和取消按钮无法点击&#10;- 点击菜单中的设置的时候，弹出来的窗口不是在父窗体居中显示" />
    <option name="LAST_COMMIT_MESSAGE" value="# 功能优化&#10;- 点击编辑选中步骤按钮后，弹出来的窗口，因为高度不够，导致确定和取消按钮无法点击&#10;- 点击菜单中的设置的时候，弹出来的窗口不是在父窗体居中显示" />
  </component>
  <component name="com.intellij.coverage.CoverageDataManagerImpl">
    <SUITE FILE_PATH="coverage/PythonTuSe$test_jules_solution.coverage" NAME="test_jules_solution Coverage Results" MODIFIED="1751016678475" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PythonTuSe$test_import_only.coverage" NAME="test_import_only Coverage Results" MODIFIED="1751417671349" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PythonTuSe$automation_ui.coverage" NAME="automation_ui Coverage Results" MODIFIED="1751447839964" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PythonTuSe$button_style_demo.coverage" NAME="button_style_demo Coverage Results" MODIFIED="1751421185514" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
    <SUITE FILE_PATH="coverage/PythonTuSe$improved_search_solution.coverage" NAME="improved_search_solution Coverage Results" MODIFIED="1751015213098" SOURCE_PROVIDER="com.intellij.coverage.DefaultCoverageFileProvider" RUNNER="coverage.py" COVERAGE_BY_TEST_ENABLED="true" COVERAGE_TRACING_ENABLED="false" WORKING_DIRECTORY="$PROJECT_DIR$" />
  </component>
</project>